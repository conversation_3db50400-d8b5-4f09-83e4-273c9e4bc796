"""
Tavily-powered search tools for finding relevant documentation, code examples, and resources.
Provides comprehensive search, extract, and crawl capabilities optimized for framework learning.
"""

import asyncio
import json
import re
from typing import List, Dict, Any, Optional, Union
from urllib.parse import quote, urlparse
import logging
import os
from datetime import datetime

from langchain_core.tools import BaseTool, Tool
from pydantic import BaseModel, Field
from langchain.tools import tool
from ..core.rate_limiter import api_manager
from ..core.error_handling import handle_tool_error, ErrorContext, ErrorCategory, ErrorSeverity, error_handler

# Tavily imports
try:
    from tavily import TavilyClient, AsyncTavilyClient
    TAVILY_AVAILABLE = True
except ImportError:
    TAVILY_AVAILABLE = False
    TavilyClient = None
    AsyncTavilyClient = None

logger = logging.getLogger(__name__)


class SearchResult(BaseModel):
    """Represents a search result."""
    title: str = Field(description="Title of the result")
    url: str = Field(description="URL of the result")
    content: str = Field(description="Content snippet")
    relevance_score: float = Field(default=0.0, description="Relevance score (0-1)")
    source: str = Field(description="Source of the result")
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ExtractResult(BaseModel):
    """Represents an extraction result."""
    url: str = Field(description="URL of the extracted content")
    content: str = Field(description="Extracted content")
    images: List[str] = Field(default_factory=list, description="Extracted images")
    success: bool = Field(description="Whether extraction was successful")
    error: Optional[str] = Field(default=None, description="Error message if failed")


class CrawlResult(BaseModel):
    """Represents a crawl result."""
    base_url: str = Field(description="Base URL that was crawled")
    pages: List[Dict[str, str]] = Field(description="List of crawled pages")
    total_pages: int = Field(description="Total number of pages crawled")
    response_time: float = Field(description="Time taken for crawl")


class TavilySearchTool(BaseTool):
    """Advanced Tavily search tool optimized for framework learning and documentation discovery."""
    
    name: str = "tavily_search"
    description: str = """
    AI-optimized search tool using Tavily API for finding current, accurate information about 
    programming frameworks, documentation, tutorials, and code examples. Perfect for discovering 
    the latest syntax, classes, methods, and best practices for any framework.
    
    Input: A natural language search query (e.g., "LangChain ChatPromptTemplate syntax", "FastAPI dependency injection tutorial")
    Output: Structured search results with AI-generated summaries, relevant content, and reliable sources.
    """
    
    api_key: Optional[str] = Field(default=None)
    max_results: int = Field(default=7)
    search_depth: str = Field(default="advanced")  # "basic" or "advanced"
    include_answer: bool = Field(default=True)
    include_raw_content: bool = Field(default=False)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.api_key:
            self.api_key = os.getenv('TAVILY_API_KEY')
    
    @handle_tool_error("tavily_search")
    def _run(self, query: str) -> str:
        """Search using Tavily API for framework-related content with rate limiting."""
        if not TAVILY_AVAILABLE:
            return "❌ Tavily search is not available. Please install tavily-python: pip install tavily-python"

        if not self.api_key:
            return "❌ Tavily API key not configured. Please set TAVILY_API_KEY environment variable."

        try:
            # Use rate-limited API execution
            async def execute_search():
                client = TavilyClient(api_key=self.api_key)

                # Execute search with parameters optimized for learning
                return client.search(
                    query=query,
                    max_results=min(self.max_results, 5),  # Limit results to reduce API usage
                    search_depth=self.search_depth,
                    include_answer=self.include_answer,
                    include_raw_content=self.include_raw_content,
                    include_images=False,  # Focus on text content for learning
                    topic="general"  # Only "general" and "news" are valid categories
                )

            # Execute with rate limiting and caching
            import asyncio
            response = asyncio.run(api_manager.execute_with_rate_limit(
                "tavily_search",
                execute_search,
                params={"query": query, "max_results": self.max_results}
            ))
            
            # Format results for AI consumption and learning
            formatted_results = []
            
            # Add the AI-generated answer if available
            if self.include_answer and response.get('answer'):
                formatted_results.append(f"🎯 **AI Summary:**\n{response['answer']}\n")
            
            # Add search results with enhanced formatting for learning
            if response.get('results'):
                formatted_results.append("📚 **Learning Resources:**")
                for i, result in enumerate(response['results'], 1):
                    title = result.get('title', 'No Title')
                    url = result.get('url', '')
                    content = result.get('content', '')
                    score = result.get('score', 0.0)
                    
                    # Detect content type for better categorization
                    content_type = self._detect_content_type(url, title, content)
                    
                    formatted_results.append(f"""
**{i}. {content_type} {title}**
   🔗 URL: {url}
   📊 Relevance: {score:.2f}
   📝 Content: {content[:400]}{'...' if len(content) > 400 else ''}
""")
            
            # Add search metadata
            response_time = response.get('response_time', 0)
            formatted_results.append(f"\n⏱️ Search completed in {response_time:.2f}s")
            
            return "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"Tavily search error: {e}")
            error_msg = str(e)

            # Provide helpful error messages for common issues
            if "usage limit" in error_msg.lower():
                return "❌ Search temporarily unavailable due to API usage limits. Using cached results when available. Please try again in a few minutes."
            elif "rate limit" in error_msg.lower():
                return "❌ Search rate limit exceeded. Please wait a moment before trying again."
            elif "api key" in error_msg.lower():
                return "❌ API authentication failed. Please check your Tavily API key configuration."
            else:
                return f"❌ Search failed: {error_msg}"
    
    def _detect_content_type(self, url: str, title: str, content: str) -> str:
        """Detect the type of content for better categorization."""
        url_lower = url.lower()
        title_lower = title.lower()
        content_lower = content.lower()
        
        if 'docs' in url_lower or 'documentation' in title_lower:
            return "📖 [DOCS]"
        elif 'github.com' in url_lower:
            return "💻 [CODE]"
        elif 'tutorial' in title_lower or 'guide' in title_lower:
            return "🎓 [TUTORIAL]"
        elif 'stackoverflow' in url_lower:
            return "❓ [Q&A]"
        elif 'blog' in url_lower or 'medium.com' in url_lower:
            return "📝 [BLOG]"
        elif any(word in content_lower for word in ['class ', 'def ', 'function', 'import']):
            return "🔧 [CODE EXAMPLE]"
        else:
            return "🌐 [WEB]"


class TavilyExtractTool(BaseTool):
    """Tavily extract tool for getting detailed content from specific URLs."""
    
    name: str = "tavily_extract"
    description: str = """
    Extract detailed content from specific URLs using Tavily Extract API. Perfect for getting 
    comprehensive documentation, tutorials, or code examples from known URLs. Use this when you 
    need the full content of specific pages for learning framework syntax, classes, and methods.
    
    Input: URL or comma-separated list of URLs to extract content from (max 20 URLs)
    Output: Extracted content in markdown format with images and structured data.
    """
    
    api_key: Optional[str] = Field(default=None)
    extract_depth: str = Field(default="advanced")  # "basic" or "advanced"
    include_images: bool = Field(default=True)
    format: str = Field(default="markdown")  # "markdown" or "text"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.api_key:
            self.api_key = os.getenv('TAVILY_API_KEY')
    
    def _run(self, urls: str) -> str:
        """Extract content from URLs using Tavily Extract API with rate limiting."""
        if not TAVILY_AVAILABLE:
            return "❌ Tavily extract is not available. Please install tavily-python: pip install tavily-python"

        if not self.api_key:
            return "❌ Tavily API key not configured. Please set TAVILY_API_KEY environment variable."

        try:
            # Parse URLs from input
            url_list = [url.strip() for url in urls.split(',') if url.strip()]
            if len(url_list) > 10:  # Reduced limit to be more conservative
                return "❌ Too many URLs. Maximum 10 URLs allowed per request to respect API limits."

            # Use rate-limited API execution
            async def execute_extract():
                client = TavilyClient(api_key=self.api_key)

                # Execute extraction
                return client.extract(
                    urls=url_list if len(url_list) > 1 else url_list[0],
                    extract_depth=self.extract_depth,
                    include_images=self.include_images,
                    format=self.format,
                    timeout=60
                )

            # Execute with rate limiting and caching
            import asyncio
            response = asyncio.run(api_manager.execute_with_rate_limit(
                "tavily_extract",
                execute_extract,
                params={"urls": urls, "extract_depth": self.extract_depth}
            ))
            
            # Format results for learning
            formatted_results = []
            formatted_results.append(f"📄 **Content Extraction Results** ({len(url_list)} URLs)")
            
            # Process successful extractions
            if response.get('results'):
                for i, result in enumerate(response['results'], 1):
                    url = result.get('url', '')
                    content = result.get('raw_content', '')
                    images = result.get('images', [])
                    
                    # Detect framework and content type
                    framework_info = self._detect_framework_content(url, content)
                    
                    formatted_results.append(f"""
**{i}. {framework_info['type']} - {framework_info['framework']}**
🔗 **URL:** {url}
📊 **Content Length:** {len(content):,} characters
🖼️ **Images Found:** {len(images)} images

**📚 Key Content Preview:**
{content[:800]}{'...' if len(content) > 800 else ''}
""")
                    
                    if images and self.include_images:
                        formatted_results.append(f"🖼️ **Images:** {', '.join(images[:5])}")
                        if len(images) > 5:
                            formatted_results.append(f"   ... and {len(images) - 5} more images")
            
            # Process failed extractions
            if response.get('failed_results'):
                formatted_results.append("\n❌ **Failed Extractions:**")
                for failed in response['failed_results']:
                    url = failed.get('url', '')
                    error = failed.get('error', 'Unknown error')
                    formatted_results.append(f"   • {url}: {error}")
            
            # Add response metadata
            response_time = response.get('response_time', 0)
            formatted_results.append(f"\n⏱️ Extraction completed in {response_time:.2f}s")
            
            return "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"Tavily extract error: {e}")
            return f"❌ Extract failed: {str(e)}"
    
    def _detect_framework_content(self, url: str, content: str) -> Dict[str, str]:
        """Detect framework and content type from URL and content."""
        url_lower = url.lower()
        content_lower = content.lower()
        
        # Framework detection
        framework = "Unknown"
        if 'langchain' in url_lower or 'langchain' in content_lower:
            framework = "LangChain"
        elif 'langgraph' in url_lower or 'langgraph' in content_lower:
            framework = "LangGraph"
        elif 'fastapi' in url_lower or 'fastapi' in content_lower:
            framework = "FastAPI"
        elif 'streamlit' in url_lower or 'streamlit' in content_lower:
            framework = "Streamlit"
        elif 'pydantic' in url_lower or 'pydantic' in content_lower:
            framework = "Pydantic"
        elif 'crewai' in url_lower or 'crewai' in content_lower:
            framework = "CrewAI"
        elif 'autogen' in url_lower or 'autogen' in content_lower:
            framework = "AutoGen"
        
        # Content type detection
        content_type = "Documentation"
        if 'class ' in content_lower and 'def ' in content_lower:
            content_type = "API Reference"
        elif 'tutorial' in url_lower or 'guide' in url_lower:
            content_type = "Tutorial"
        elif 'example' in url_lower or 'example' in content_lower:
            content_type = "Code Examples"
        elif 'cookbook' in url_lower:
            content_type = "Cookbook"
        
        return {"framework": framework, "type": content_type}


class TavilyCrawlTool(BaseTool):
    """Tavily crawl tool for discovering and extracting content from entire websites."""
    
    name: str = "tavily_crawl"
    description: str = """
    Intelligently crawl websites to discover and extract comprehensive documentation, tutorials, 
    and learning materials. Perfect for exploring framework documentation sites, finding all 
    relevant pages, syntax examples, class definitions, and method documentation.
    
    Input: Base URL and optional instructions (e.g., "https://docs.langchain.com" with instructions "Find ChatPromptTemplate documentation")
    Output: Structured crawl results with discovered pages, content summaries, and learning paths.
    """
    
    api_key: Optional[str] = Field(default=None)
    max_depth: int = Field(default=2)
    max_breadth: int = Field(default=15)
    limit: int = Field(default=30)
    extract_depth: str = Field(default="basic")
    include_images: bool = Field(default=False)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.api_key:
            self.api_key = os.getenv('TAVILY_API_KEY')
    
    def _run(self, url_and_instructions: str) -> str:
        """Crawl website using Tavily Crawl API for comprehensive learning content."""
        if not TAVILY_AVAILABLE:
            return "❌ Tavily crawl is not available. Please install tavily-python: pip install tavily-python"
        
        if not self.api_key:
            return "❌ Tavily API key not configured. Please set TAVILY_API_KEY environment variable."
        
        try:
            # Parse input (URL and optional instructions)
            parts = url_and_instructions.split(' | ')
            base_url = parts[0].strip()
            instructions = parts[1].strip() if len(parts) > 1 else None

            # Validate URL
            if not self._is_valid_url(base_url):
                return f"❌ Invalid URL provided: {base_url}"

            # Set up crawl parameters based on URL type
            crawl_params = self._get_crawl_parameters(base_url, instructions)

            # Use rate-limited API execution
            async def execute_crawl():
                client = TavilyClient(api_key=self.api_key)

                # Execute crawl with reduced limits to be more conservative
                return client.crawl(
                    url=base_url,
                    max_depth=min(self.max_depth, 2),  # Limit depth
                    max_breadth=min(self.max_breadth, 10),  # Limit breadth
                    limit=min(self.limit, 20),  # Limit total pages
                    instructions=instructions,
                    extract_depth=self.extract_depth,
                    include_images=self.include_images,
                    **crawl_params
                )

            # Execute with rate limiting and caching
            import asyncio
            response = asyncio.run(api_manager.execute_with_rate_limit(
                "tavily_crawl",
                execute_crawl,
                params={"url": base_url, "instructions": instructions}
            ))
            
            # Format results for learning
            formatted_results = []
            base_url_info = response.get('base_url', base_url)
            pages = response.get('results', [])
            response_time = response.get('response_time', 0)
            
            # Header
            framework = self._detect_framework_from_url(base_url_info)
            formatted_results.append(f"🕷️ **{framework} Documentation Crawl Results**")
            formatted_results.append(f"🏠 **Base URL:** {base_url_info}")
            if instructions:
                formatted_results.append(f"🎯 **Instructions:** {instructions}")
            formatted_results.append(f"📄 **Pages Found:** {len(pages)}")
            formatted_results.append(f"⏱️ **Crawl Time:** {response_time:.2f}s\n")
            
            # Organize pages by type and importance
            organized_pages = self._organize_crawled_pages(pages)
            
            # Display results by category
            for category, page_list in organized_pages.items():
                if page_list:
                    formatted_results.append(f"## {category}")
                    for i, page in enumerate(page_list[:5], 1):  # Limit to top 5 per category
                        url = page.get('url', '')
                        content = page.get('raw_content', '')
                        
                        # Extract key information from content
                        key_info = self._extract_key_learning_info(content)
                        
                        formatted_results.append(f"""
**{i}. {self._get_page_title(url, content)}**
🔗 **URL:** {url}
📝 **Content Length:** {len(content):,} characters
💡 **Key Learning Points:**
{key_info}
""")
                    
                    if len(page_list) > 5:
                        formatted_results.append(f"   ... and {len(page_list) - 5} more {category.lower()} pages")
                    formatted_results.append("")
            
            # Add learning path suggestions
            learning_path = self._generate_learning_path(organized_pages, framework)
            if learning_path:
                formatted_results.append("🎓 **Suggested Learning Path:**")
                formatted_results.append(learning_path)
            
            return "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"Tavily crawl error: {e}")
            return f"❌ Crawl failed: {str(e)}"
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format."""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    def _get_crawl_parameters(self, url: str, instructions: str) -> Dict[str, Any]:
        """Get optimized crawl parameters based on URL and instructions."""
        params = {}
        
        # Focus on documentation paths for known frameworks
        if 'docs' in url or 'documentation' in url:
            params['select_paths'] = ['/docs/.*', '/documentation/.*', '/guide/.*', '/tutorial/.*']
            params['categories'] = ['Documentation']  # Remove invalid 'Developers' category
        elif 'github.com' in url:
            params['select_paths'] = ['/blob/.*', '/tree/.*', '/docs/.*', '/examples/.*']
        
        # Exclude non-learning content
        params['exclude_paths'] = ['/admin/.*', '/login/.*', '/signup/.*', '/blog/.*', '/news/.*']
        
        return params
    
    def _detect_framework_from_url(self, url: str) -> str:
        """Detect framework from URL."""
        url_lower = url.lower()
        if 'langchain' in url_lower:
            return "LangChain"
        elif 'langgraph' in url_lower:
            return "LangGraph"
        elif 'fastapi' in url_lower:
            return "FastAPI"
        elif 'streamlit' in url_lower:
            return "Streamlit"
        elif 'pydantic' in url_lower:
            return "Pydantic"
        elif 'crewai' in url_lower:
            return "CrewAI"
        elif 'autogen' in url_lower:
            return "AutoGen"
        else:
            return "Framework"
    
    def _organize_crawled_pages(self, pages: List[Dict]) -> Dict[str, List[Dict]]:
        """Organize crawled pages by type for better learning structure."""
        organized = {
            "📖 Core Documentation": [],
            "🎓 Tutorials & Guides": [],
            "🔧 API Reference": [],
            "💡 Examples & Cookbook": [],
            "🚀 Getting Started": [],
            "🔍 Other Resources": []
        }
        
        for page in pages:
            url = page.get('url', '').lower()
            content = page.get('raw_content', '').lower()
            
            if any(term in url for term in ['getting-started', 'quickstart', 'installation']):
                organized["🚀 Getting Started"].append(page)
            elif any(term in url for term in ['tutorial', 'guide', 'walkthrough']):
                organized["🎓 Tutorials & Guides"].append(page)
            elif any(term in url for term in ['api', 'reference', 'modules']):
                organized["🔧 API Reference"].append(page)
            elif any(term in url for term in ['example', 'cookbook', 'how-to']):
                organized["💡 Examples & Cookbook"].append(page)
            elif any(term in url for term in ['docs', 'documentation']):
                organized["📖 Core Documentation"].append(page)
            else:
                organized["🔍 Other Resources"].append(page)
        
        return organized
    
    def _get_page_title(self, url: str, content: str) -> str:
        """Extract page title from URL or content."""
        # Try to extract title from content
        title_match = re.search(r'#\s+([^\n]+)', content)
        if title_match:
            return title_match.group(1).strip()
        
        # Fallback to URL path
        path = urlparse(url).path
        return path.split('/')[-1].replace('-', ' ').replace('_', ' ').title() or "Page"
    
    def _extract_key_learning_info(self, content: str) -> str:
        """Extract key learning information from page content."""
        lines = content.split('\n')
        key_points = []
        
        # Look for class definitions, functions, and important concepts
        for line in lines[:50]:  # Check first 50 lines
            line = line.strip()
            if re.match(r'^class\s+\w+', line):
                key_points.append(f"   • Class: {line}")
            elif re.match(r'^def\s+\w+', line):
                key_points.append(f"   • Function: {line}")
            elif line.startswith('##') and len(line) < 100:
                key_points.append(f"   • Section: {line.replace('#', '').strip()}")
            elif 'import' in line and len(line) < 80:
                key_points.append(f"   • Import: {line}")
        
        if not key_points:
            # If no code found, look for key headings
            for line in lines[:20]:
                if line.startswith('#') and len(line) < 100:
                    key_points.append(f"   • {line.replace('#', '').strip()}")
        
        return '\n'.join(key_points[:3]) if key_points else "   • General documentation content"
    
    def _generate_learning_path(self, organized_pages: Dict[str, List], framework: str) -> str:
        """Generate a suggested learning path based on discovered content."""
        path = []
        
        if organized_pages["🚀 Getting Started"]:
            path.append("1. Start with Getting Started guides")
        if organized_pages["🎓 Tutorials & Guides"]:
            path.append("2. Follow Tutorials & Guides")
        if organized_pages["💡 Examples & Cookbook"]:
            path.append("3. Explore Examples & Cookbook")
        if organized_pages["🔧 API Reference"]:
            path.append("4. Dive into API Reference")
        if organized_pages["📖 Core Documentation"]:
            path.append("5. Study Core Documentation")
        
        if path:
            return '\n'.join([f"   {step}" for step in path])
        else:
            return f"   Explore the discovered {framework} resources systematically"


# Framework-specific search tools
@tool
def search_framework_documentation(framework: str, topic: str) -> str:
    """
    Search for official documentation about a specific framework topic using AI-optimized Tavily search.
    Perfect for finding syntax, classes, methods, and official guidelines.
    
    Args:
        framework: The framework name (e.g., "langchain", "fastapi", "streamlit")
        topic: The specific topic to search for (e.g., "ChatPromptTemplate", "dependency injection")
        
    Returns:
        AI-summarized search results focused on official documentation with syntax and examples
    """
    search_tool = TavilySearchTool()
    query = f"{framework} {topic} official documentation syntax examples"
    return search_tool._run(query)


@tool
def extract_learning_content(urls: str) -> str:
    """
    Extract comprehensive learning content from specific URLs using Tavily Extract.
    Ideal for getting detailed documentation, tutorials, or code examples from known sources.
    
    Args:
        urls: URL or comma-separated list of URLs to extract content from (max 20 URLs)
        
    Returns:
        Extracted content in markdown format with structured learning information
    """
    extract_tool = TavilyExtractTool()
    return extract_tool._run(urls)


@tool
def crawl_framework_docs(base_url: str, instructions: str = None) -> str:
    """
    Intelligently crawl framework documentation websites to discover comprehensive learning materials.
    Perfect for exploring entire documentation sites and finding all relevant syntax and class information.
    
    Args:
        base_url: The base URL to start crawling from (e.g., "https://docs.langchain.com")
        instructions: Optional natural language instructions for the crawler
        
    Returns:
        Organized crawl results with learning paths and discovered documentation
    """
    crawl_tool = TavilyCrawlTool()
    
    # Combine URL and instructions for the tool
    url_and_instructions = base_url
    if instructions:
        url_and_instructions += f" | {instructions}"
    
    return crawl_tool._run(url_and_instructions)


@tool
def search_framework_syntax(framework: str, component: str) -> str:
    """
    Search for specific framework syntax and class definitions using advanced AI search.
    Optimized for finding exact syntax, method signatures, and usage examples.
    
    Args:
        framework: The framework name (e.g., "langchain", "pydantic", "fastapi")
        component: The specific component to search for (e.g., "BaseModel", "ChatPromptTemplate")
        
    Returns:
        Detailed syntax information with code examples and usage patterns
    """
    search_tool = TavilySearchTool()
    query = f"{framework} {component} syntax class definition methods examples code"
    return search_tool._run(query)


@tool
def discover_framework_learning_path(framework: str) -> str:
    """
    Discover comprehensive learning resources and create a structured learning path for a framework.
    Combines search and crawling to find the best tutorials, documentation, and examples.
    
    Args:
        framework: The framework name to create a learning path for
        
    Returns:
        Comprehensive learning path with tutorials, documentation, and practical examples
    """
    search_tool = TavilySearchTool()
    
    # Search for comprehensive learning resources
    queries = [
        f"{framework} official documentation getting started",
        f"{framework} tutorial guide beginner advanced",
        f"{framework} best practices examples cookbook",
        f"{framework} API reference class methods"
    ]
    
    all_results = []
    for query in queries:
        result = search_tool._run(query)
        all_results.append(result)
    
    # Combine and format results
    formatted_result = f"""🎓 **Comprehensive Learning Path for {framework.title()}**

This learning path combines the best available resources for mastering {framework}:

{'=' * 60}

""" + '\n\n'.join(all_results)
    
    return formatted_result


def get_search_tools() -> List[Tool]:
    """Get all available Tavily-powered search tools."""
    tools = []
    
    # Core Tavily tools
    if TAVILY_AVAILABLE and os.getenv('TAVILY_API_KEY'):
        # Create instances first to avoid attribute access issues
        tavily_search_instance = TavilySearchTool()
        tavily_extract_instance = TavilyExtractTool()
        tavily_crawl_instance = TavilyCrawlTool()
        
        # Add direct tool instances
        tools.extend([
            Tool(
                name="tavily_search",
                description=tavily_search_instance.description,
                func=tavily_search_instance._run
            ),
            Tool(
                name="tavily_extract", 
                description=tavily_extract_instance.description,
                func=tavily_extract_instance._run
            ),
            Tool(
                name="tavily_crawl",
                description=tavily_crawl_instance.description,
                func=tavily_crawl_instance._run
            )
        ])
    
    # Framework-specific learning tools
    tools.extend([
        search_framework_documentation,
        extract_learning_content,
        crawl_framework_docs,
        search_framework_syntax,
        discover_framework_learning_path
    ])
    
    return tools


# Global tool instances for easy access
tavily_search = TavilySearchTool()
tavily_extract = TavilyExtractTool()
tavily_crawl = TavilyCrawlTool()


def check_tavily_availability() -> Dict[str, bool]:
    """Check if Tavily is properly configured and available."""
    return {
        "tavily_installed": TAVILY_AVAILABLE,
        "api_key_configured": bool(os.getenv('TAVILY_API_KEY')),
        "ready": TAVILY_AVAILABLE and bool(os.getenv('TAVILY_API_KEY'))
    }


if __name__ == "__main__":
    # Test the tools
    status = check_tavily_availability()
    print("Tavily Search Tools Status:", status)
    
    if status["ready"]:
        # Test search
        search_result = tavily_search._run("LangChain ChatPromptTemplate syntax")
        print("Search Result:", search_result[:200])