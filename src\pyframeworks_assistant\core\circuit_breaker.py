"""
Circuit breaker pattern implementation for tool execution reliability.
Provides fault tolerance and prevents cascading failures in tool execution chains.
"""

import time
import logging
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import asyncio
from functools import wraps

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, calls fail fast
    HALF_OPEN = "half_open"  # Testing if service has recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5  # Number of failures before opening
    recovery_timeout: int = 60  # Seconds before trying half-open
    success_threshold: int = 3  # Successes needed to close from half-open
    timeout: int = 30  # Timeout for individual operations
    max_retries: int = 3  # Maximum retry attempts


@dataclass
class ExecutionMetrics:
    """Metrics for tool execution."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    average_response_time: float = 0.0
    response_times: List[float] = field(default_factory=list)


class CircuitBreaker:
    """Circuit breaker for tool execution with failure isolation."""
    
    def __init__(self, name: str, config: CircuitBreakerConfig = None):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.state = CircuitState.CLOSED
        self.metrics = ExecutionMetrics()
        self.last_failure_time = None
        self.lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        async with self.lock:
            # Check if circuit should be opened
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    logger.info(f"Circuit breaker {self.name} transitioning to HALF_OPEN")
                else:
                    raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is OPEN")
            
            # Execute the function
            start_time = time.time()
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await asyncio.wait_for(func(*args, **kwargs), timeout=self.config.timeout)
                else:
                    result = await asyncio.wait_for(
                        asyncio.get_event_loop().run_in_executor(None, func, *args, **kwargs),
                        timeout=self.config.timeout
                    )
                
                # Record success
                execution_time = time.time() - start_time
                await self._record_success(execution_time)
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                await self._record_failure(e, execution_time)
                raise
    
    async def _record_success(self, execution_time: float):
        """Record successful execution."""
        self.metrics.total_calls += 1
        self.metrics.successful_calls += 1
        self.metrics.consecutive_successes += 1
        self.metrics.consecutive_failures = 0
        self.metrics.last_success_time = datetime.now()
        
        # Update response time metrics
        self.metrics.response_times.append(execution_time)
        if len(self.metrics.response_times) > 100:  # Keep last 100 measurements
            self.metrics.response_times.pop(0)
        self.metrics.average_response_time = sum(self.metrics.response_times) / len(self.metrics.response_times)
        
        # Transition state based on success
        if self.state == CircuitState.HALF_OPEN:
            if self.metrics.consecutive_successes >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                logger.info(f"Circuit breaker {self.name} transitioning to CLOSED")
    
    async def _record_failure(self, error: Exception, execution_time: float):
        """Record failed execution."""
        self.metrics.total_calls += 1
        self.metrics.failed_calls += 1
        self.metrics.consecutive_failures += 1
        self.metrics.consecutive_successes = 0
        self.metrics.last_failure_time = datetime.now()
        self.last_failure_time = time.time()
        
        logger.warning(f"Circuit breaker {self.name} recorded failure: {error}")
        
        # Transition to OPEN if failure threshold exceeded
        if (self.state == CircuitState.CLOSED and 
            self.metrics.consecutive_failures >= self.config.failure_threshold):
            self.state = CircuitState.OPEN
            logger.error(f"Circuit breaker {self.name} transitioning to OPEN after {self.metrics.consecutive_failures} failures")
        elif self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker {self.name} returning to OPEN from HALF_OPEN")
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        return time.time() - self.last_failure_time >= self.config.recovery_timeout
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current circuit breaker metrics."""
        success_rate = 0.0
        if self.metrics.total_calls > 0:
            success_rate = self.metrics.successful_calls / self.metrics.total_calls
        
        return {
            "name": self.name,
            "state": self.state.value,
            "total_calls": self.metrics.total_calls,
            "successful_calls": self.metrics.successful_calls,
            "failed_calls": self.metrics.failed_calls,
            "success_rate": success_rate,
            "consecutive_failures": self.metrics.consecutive_failures,
            "consecutive_successes": self.metrics.consecutive_successes,
            "average_response_time": self.metrics.average_response_time,
            "last_failure_time": self.metrics.last_failure_time.isoformat() if self.metrics.last_failure_time else None,
            "last_success_time": self.metrics.last_success_time.isoformat() if self.metrics.last_success_time else None
        }


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open."""
    pass


class ToolExecutionManager:
    """Manages tool execution with circuit breakers and retry logic."""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.global_config = CircuitBreakerConfig()
    
    def get_circuit_breaker(self, tool_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for a tool."""
        if tool_name not in self.circuit_breakers:
            # Create tool-specific configuration
            config = CircuitBreakerConfig()
            
            # Adjust config based on tool type
            if "search" in tool_name.lower() or "tavily" in tool_name.lower():
                config.failure_threshold = 3  # More sensitive for external APIs
                config.recovery_timeout = 120  # Longer recovery for external services
            elif "execute" in tool_name.lower() or "code" in tool_name.lower():
                config.failure_threshold = 2  # Very sensitive for code execution
                config.timeout = 60  # Longer timeout for code execution
            
            self.circuit_breakers[tool_name] = CircuitBreaker(tool_name, config)
        
        return self.circuit_breakers[tool_name]
    
    async def execute_tool_safely(self, tool_name: str, tool_func: Callable, *args, **kwargs) -> Any:
        """Execute tool with circuit breaker protection and retry logic."""
        circuit_breaker = self.get_circuit_breaker(tool_name)
        
        last_exception = None
        for attempt in range(self.global_config.max_retries + 1):
            try:
                result = await circuit_breaker.call(tool_func, *args, **kwargs)
                if attempt > 0:
                    logger.info(f"Tool {tool_name} succeeded on attempt {attempt + 1}")
                return result
                
            except CircuitBreakerOpenError:
                # Don't retry if circuit breaker is open
                logger.error(f"Tool {tool_name} circuit breaker is open, failing fast")
                raise
                
            except Exception as e:
                last_exception = e
                if attempt < self.global_config.max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Tool {tool_name} failed on attempt {attempt + 1}, retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"Tool {tool_name} failed after {attempt + 1} attempts: {e}")
        
        # All retries exhausted
        raise last_exception
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get metrics for all circuit breakers."""
        return {
            name: breaker.get_metrics() 
            for name, breaker in self.circuit_breakers.items()
        }
    
    def reset_circuit_breaker(self, tool_name: str):
        """Manually reset a circuit breaker."""
        if tool_name in self.circuit_breakers:
            self.circuit_breakers[tool_name].state = CircuitState.CLOSED
            self.circuit_breakers[tool_name].metrics = ExecutionMetrics()
            logger.info(f"Circuit breaker {tool_name} manually reset")
    
    def reset_all_circuit_breakers(self):
        """Reset all circuit breakers."""
        for tool_name in self.circuit_breakers:
            self.reset_circuit_breaker(tool_name)
        logger.info("All circuit breakers reset")


# Global instance
tool_execution_manager = ToolExecutionManager()


def circuit_breaker(tool_name: str = None):
    """Decorator for adding circuit breaker protection to functions."""
    def decorator(func):
        nonlocal tool_name
        if tool_name is None:
            tool_name = func.__name__
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await tool_execution_manager.execute_tool_safely(tool_name, func, *args, **kwargs)
        
        return wrapper
    return decorator
