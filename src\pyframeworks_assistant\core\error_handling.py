"""
Standardized error handling system for consistent error management across all tools and agents.
Provides unified error types, logging, and recovery mechanisms.
"""

import logging
import traceback
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field
import asyncio
from functools import wraps

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for better classification."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    NETWORK = "network"
    TIMEOUT = "timeout"
    RATE_LIMIT = "rate_limit"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"
    EXTERNAL_SERVICE = "external_service"
    INTERNAL = "internal"
    USER_INPUT = "user_input"
    SECURITY = "security"


@dataclass
class ErrorContext:
    """Context information for errors."""
    tool_name: Optional[str] = None
    agent_name: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StandardError:
    """Standardized error representation."""
    code: str
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    context: ErrorContext
    original_exception: Optional[Exception] = None
    stack_trace: Optional[str] = None
    recovery_suggestions: List[str] = field(default_factory=list)
    user_message: Optional[str] = None


class ErrorHandler:
    """Centralized error handling with consistent logging and recovery."""
    
    def __init__(self):
        self.error_history: List[StandardError] = []
        self.max_history = 1000
        self.error_counts: Dict[str, int] = {}
    
    def handle_error(self, 
                    exception: Exception,
                    context: ErrorContext,
                    category: ErrorCategory = ErrorCategory.INTERNAL,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    user_message: Optional[str] = None,
                    recovery_suggestions: List[str] = None) -> StandardError:
        """Handle an error with standardized processing."""
        
        # Generate error code
        error_code = self._generate_error_code(exception, category, context)
        
        # Create standardized error
        std_error = StandardError(
            code=error_code,
            message=str(exception),
            category=category,
            severity=severity,
            context=context,
            original_exception=exception,
            stack_trace=traceback.format_exc(),
            recovery_suggestions=recovery_suggestions or [],
            user_message=user_message or self._generate_user_message(exception, category)
        )
        
        # Log the error
        self._log_error(std_error)
        
        # Store in history
        self._store_error(std_error)
        
        # Update error counts
        self.error_counts[error_code] = self.error_counts.get(error_code, 0) + 1
        
        return std_error
    
    def _generate_error_code(self, exception: Exception, category: ErrorCategory, context: ErrorContext) -> str:
        """Generate a unique error code."""
        exception_type = type(exception).__name__
        tool_name = context.tool_name or "unknown"
        timestamp = context.timestamp.strftime("%Y%m%d_%H%M%S")
        
        return f"{category.value.upper()}_{exception_type}_{tool_name}_{timestamp}"
    
    def _generate_user_message(self, exception: Exception, category: ErrorCategory) -> str:
        """Generate user-friendly error message."""
        exception_str = str(exception).lower()
        
        if category == ErrorCategory.VALIDATION:
            return "Invalid input provided. Please check your request and try again."
        elif category == ErrorCategory.AUTHENTICATION:
            return "Authentication failed. Please check your API keys and credentials."
        elif category == ErrorCategory.RATE_LIMIT:
            return "Service temporarily unavailable due to rate limits. Please try again in a few minutes."
        elif category == ErrorCategory.NETWORK:
            return "Network connection issue. Please check your internet connection and try again."
        elif category == ErrorCategory.TIMEOUT:
            return "Request timed out. Please try again or reduce the complexity of your request."
        elif category == ErrorCategory.EXTERNAL_SERVICE:
            if "usage limit" in exception_str:
                return "External service usage limit reached. Please try again later."
            elif "api key" in exception_str:
                return "External service authentication failed. Please check API configuration."
            else:
                return "External service is temporarily unavailable. Please try again later."
        elif category == ErrorCategory.RESOURCE:
            return "System resources are temporarily limited. Please try again with a simpler request."
        elif category == ErrorCategory.SECURITY:
            return "Security validation failed. Your request contains potentially unsafe content."
        else:
            return "An unexpected error occurred. Please try again or contact support if the issue persists."
    
    def _log_error(self, error: StandardError):
        """Log error with appropriate level."""
        log_message = f"[{error.code}] {error.message}"
        
        if error.context.tool_name:
            log_message += f" | Tool: {error.context.tool_name}"
        if error.context.agent_name:
            log_message += f" | Agent: {error.context.agent_name}"
        if error.context.user_id:
            log_message += f" | User: {error.context.user_id}"
        
        if error.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message, exc_info=error.original_exception)
        elif error.severity == ErrorSeverity.HIGH:
            logger.error(log_message, exc_info=error.original_exception)
        elif error.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _store_error(self, error: StandardError):
        """Store error in history with size limit."""
        self.error_history.append(error)
        
        # Maintain size limit
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        if not self.error_history:
            return {"total_errors": 0}
        
        # Count by category
        category_counts = {}
        severity_counts = {}
        recent_errors = []
        
        for error in self.error_history[-100:]:  # Last 100 errors
            category_counts[error.category.value] = category_counts.get(error.category.value, 0) + 1
            severity_counts[error.severity.value] = severity_counts.get(error.severity.value, 0) + 1
            
            if (datetime.now() - error.context.timestamp).total_seconds() < 3600:  # Last hour
                recent_errors.append({
                    "code": error.code,
                    "message": error.message,
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "timestamp": error.context.timestamp.isoformat()
                })
        
        return {
            "total_errors": len(self.error_history),
            "category_counts": category_counts,
            "severity_counts": severity_counts,
            "recent_errors": recent_errors,
            "most_common_errors": sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        }
    
    def clear_old_errors(self, hours: int = 24):
        """Clear errors older than specified hours."""
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        self.error_history = [
            error for error in self.error_history
            if error.context.timestamp.timestamp() > cutoff_time
        ]


# Global error handler instance
error_handler = ErrorHandler()


def handle_tool_error(tool_name: str, agent_name: str = None):
    """Decorator for standardized tool error handling."""
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            context = ErrorContext(
                tool_name=tool_name,
                agent_name=agent_name,
                timestamp=datetime.now()
            )
            
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                # Categorize the error
                category = _categorize_exception(e)
                severity = _determine_severity(e, category)
                
                # Handle the error
                std_error = error_handler.handle_error(
                    exception=e,
                    context=context,
                    category=category,
                    severity=severity
                )
                
                # Return standardized error response
                return {
                    "success": False,
                    "error": std_error.user_message,
                    "error_code": std_error.code,
                    "category": std_error.category.value,
                    "recovery_suggestions": std_error.recovery_suggestions
                }
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            context = ErrorContext(
                tool_name=tool_name,
                agent_name=agent_name,
                timestamp=datetime.now()
            )
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Categorize the error
                category = _categorize_exception(e)
                severity = _determine_severity(e, category)
                
                # Handle the error
                std_error = error_handler.handle_error(
                    exception=e,
                    context=context,
                    category=category,
                    severity=severity
                )
                
                # Return standardized error response
                return {
                    "success": False,
                    "error": std_error.user_message,
                    "error_code": std_error.code,
                    "category": std_error.category.value,
                    "recovery_suggestions": std_error.recovery_suggestions
                }
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def _categorize_exception(exception: Exception) -> ErrorCategory:
    """Categorize exception based on type and message."""
    exception_type = type(exception).__name__
    exception_msg = str(exception).lower()
    
    if "validation" in exception_msg or "invalid" in exception_msg:
        return ErrorCategory.VALIDATION
    elif "authentication" in exception_msg or "unauthorized" in exception_msg:
        return ErrorCategory.AUTHENTICATION
    elif "rate limit" in exception_msg or "usage limit" in exception_msg:
        return ErrorCategory.RATE_LIMIT
    elif "timeout" in exception_msg or exception_type in ["TimeoutError", "asyncio.TimeoutError"]:
        return ErrorCategory.TIMEOUT
    elif "connection" in exception_msg or "network" in exception_msg:
        return ErrorCategory.NETWORK
    elif "memory" in exception_msg or "resource" in exception_msg:
        return ErrorCategory.RESOURCE
    elif "api key" in exception_msg or "credentials" in exception_msg:
        return ErrorCategory.AUTHENTICATION
    elif "security" in exception_msg or "unsafe" in exception_msg:
        return ErrorCategory.SECURITY
    else:
        return ErrorCategory.INTERNAL


def _determine_severity(exception: Exception, category: ErrorCategory) -> ErrorSeverity:
    """Determine error severity based on exception and category."""
    if category in [ErrorCategory.SECURITY, ErrorCategory.AUTHENTICATION]:
        return ErrorSeverity.HIGH
    elif category in [ErrorCategory.RATE_LIMIT, ErrorCategory.EXTERNAL_SERVICE]:
        return ErrorSeverity.MEDIUM
    elif category in [ErrorCategory.VALIDATION, ErrorCategory.USER_INPUT]:
        return ErrorSeverity.LOW
    else:
        return ErrorSeverity.MEDIUM
