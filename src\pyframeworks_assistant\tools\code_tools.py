"""
Code tools for execution, analysis, and formatting.
Provides safe code execution and analysis capabilities for learning.
"""

import ast
import subprocess
import tempfile
import os
import sys
import time
import signal
import resource
import psutil
from typing import Dict, Any, Optional, List
import logging
from pathlib import Path
import shutil

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from ..core.security import InputSanitizer, ValidationError, SecurityLevel
from ..core.error_handling import handle_tool_error, ErrorContext, ErrorCategory, ErrorSeverity, error_handler

logger = logging.getLogger(__name__)


class SecureExecutionEnvironment:
    """Secure environment for code execution with sandboxing and resource limits."""

    def __init__(self):
        self.max_execution_time = 30  # seconds
        self.max_memory_mb = 128  # MB
        self.max_file_size_mb = 10  # MB
        self.allowed_modules = {
            'langchain', 'langchain_core', 'langchain_openai', 'langchain_anthropic',
            'langchain_google_genai', 'langgraph', 'pydantic', 'typing', 'datetime',
            'json', 'math', 'random', 'string', 'collections', 'itertools', 'functools',
            'dataclasses', 'enum', 'uuid', 'time', 'asyncio', 'logging'
        }
        self.sanitizer = InputSanitizer()

    def create_sandbox_directory(self) -> Path:
        """Create a temporary sandbox directory for code execution."""
        sandbox_dir = Path(tempfile.mkdtemp(prefix="gaapf_sandbox_"))

        # Set restrictive permissions
        sandbox_dir.chmod(0o700)

        return sandbox_dir

    def validate_code_safety(self, code: str) -> bool:
        """Validate that code is safe to execute."""
        try:
            # Use the sanitizer to check for dangerous patterns
            self.sanitizer.sanitize_code_content(code, SecurityLevel.CRITICAL)

            # Additional AST-based validation
            tree = ast.parse(code)

            # Check for dangerous AST nodes
            dangerous_nodes = []
            for node in ast.walk(tree):
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    # Check imports
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            module_name = alias.name.split('.')[0]
                            if module_name not in self.allowed_modules:
                                dangerous_nodes.append(f"Unsafe import: {alias.name}")
                    elif isinstance(node, ast.ImportFrom) and node.module:
                        module_name = node.module.split('.')[0]
                        if module_name not in self.allowed_modules:
                            dangerous_nodes.append(f"Unsafe import: {node.module}")

                elif isinstance(node, ast.Call):
                    # Check for dangerous function calls
                    if isinstance(node.func, ast.Name):
                        if node.func.id in ['eval', 'exec', 'compile', '__import__']:
                            dangerous_nodes.append(f"Dangerous function call: {node.func.id}")
                    elif isinstance(node.func, ast.Attribute):
                        if node.func.attr in ['system', 'popen', 'spawn']:
                            dangerous_nodes.append(f"Dangerous method call: {node.func.attr}")

            if dangerous_nodes:
                logger.warning(f"Code validation failed: {dangerous_nodes}")
                return False

            return True

        except (SyntaxError, ValidationError) as e:
            logger.warning(f"Code validation failed: {e}")
            return False

    def set_resource_limits(self):
        """Set resource limits for the execution process."""
        try:
            # Set memory limit (in bytes)
            memory_limit = self.max_memory_mb * 1024 * 1024
            resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))

            # Set CPU time limit
            resource.setrlimit(resource.RLIMIT_CPU, (self.max_execution_time, self.max_execution_time))

            # Limit file size
            file_size_limit = self.max_file_size_mb * 1024 * 1024
            resource.setrlimit(resource.RLIMIT_FSIZE, (file_size_limit, file_size_limit))

            # Limit number of processes
            resource.setrlimit(resource.RLIMIT_NPROC, (10, 10))

        except (OSError, ValueError) as e:
            logger.warning(f"Could not set resource limits: {e}")

    def execute_code_safely(self, code: str, sandbox_dir: Path) -> Dict[str, Any]:
        """Execute code safely in a sandboxed environment."""
        if not self.validate_code_safety(code):
            return {
                "success": False,
                "error": "Code failed safety validation",
                "output": "",
                "execution_time": 0,
                "warnings": ["Code contains potentially unsafe operations"]
            }

        # Create the code file in sandbox
        code_file = sandbox_dir / "safe_code.py"

        try:
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(code)

            # Prepare execution environment
            env = os.environ.copy()
            env['PYTHONPATH'] = str(sandbox_dir)
            env['TMPDIR'] = str(sandbox_dir)
            env['HOME'] = str(sandbox_dir)

            # Remove potentially dangerous environment variables
            dangerous_env_vars = ['LD_PRELOAD', 'LD_LIBRARY_PATH', 'PYTHONSTARTUP']
            for var in dangerous_env_vars:
                env.pop(var, None)

            start_time = time.time()

            # Execute with strict limits
            try:
                process = subprocess.Popen(
                    [sys.executable, str(code_file)],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=sandbox_dir,
                    env=env,
                    preexec_fn=self.set_resource_limits if os.name != 'nt' else None
                )

                # Monitor execution
                stdout, stderr = process.communicate(timeout=self.max_execution_time)
                execution_time = time.time() - start_time

                return {
                    "success": process.returncode == 0,
                    "output": stdout,
                    "error": stderr if process.returncode != 0 else None,
                    "execution_time": execution_time,
                    "warnings": []
                }

            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                return {
                    "success": False,
                    "error": f"Execution timeout after {self.max_execution_time} seconds",
                    "output": "",
                    "execution_time": self.max_execution_time,
                    "warnings": ["Execution was terminated due to timeout"]
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Execution failed: {str(e)}",
                "output": "",
                "execution_time": 0,
                "warnings": ["Unexpected error during execution"]
            }
        finally:
            # Cleanup
            try:
                if code_file.exists():
                    code_file.unlink()
            except Exception as e:
                logger.warning(f"Failed to cleanup code file: {e}")


class CodeExecutionResult(BaseModel):
    """Result of code execution."""
    success: bool = Field(description="Whether execution was successful")
    output: str = Field(description="Execution output")
    error: Optional[str] = Field(default=None, description="Error message if any")
    execution_time: float = Field(description="Execution time in seconds")
    warnings: List[str] = Field(default_factory=list, description="Warnings generated")


class CodeAnalysisResult(BaseModel):
    """Result of code analysis."""
    is_valid: bool = Field(description="Whether code is syntactically valid")
    complexity_score: int = Field(description="Code complexity score")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    imports: List[str] = Field(default_factory=list, description="Required imports")
    functions: List[str] = Field(default_factory=list, description="Defined functions")
    classes: List[str] = Field(default_factory=list, description="Defined classes")


class CodeExecutionTool(BaseTool):
    """Tool for safe Python code execution with comprehensive security measures."""

    name: str = "code_execution"
    description: str = """
    Execute Python code safely in a sandboxed environment with resource limits.
    Use this for running code examples, testing snippets, or demonstrating concepts.

    Input: Python code string to execute
    Output: Execution result with output, errors, and security metadata

    Security Features:
    - Input validation and sanitization
    - Restricted imports (only safe modules allowed)
    - Resource limits (memory, CPU time, file size)
    - Sandboxed execution environment
    - Timeout protection
    """

    def __init__(self):
        super().__init__()
        self.secure_env = SecureExecutionEnvironment()
    
    @handle_tool_error("code_execution")
    def _run(self, code: str) -> CodeExecutionResult:
        """Execute Python code safely in a sandboxed environment."""
        if not code.strip():
            return CodeExecutionResult(
                success=False,
                output="",
                error="No code provided",
                execution_time=0,
                warnings=["Empty code input"]
            )

        # Create sandbox directory
        sandbox_dir = None
        try:
            sandbox_dir = self.secure_env.create_sandbox_directory()

            # Execute code safely
            result = self.secure_env.execute_code_safely(code, sandbox_dir)

            return CodeExecutionResult(
                success=result["success"],
                output=result["output"],
                error=result["error"],
                execution_time=result["execution_time"],
                warnings=result["warnings"]
            )

        except Exception as e:
            logger.error(f"Unexpected error in code execution: {e}")
            return CodeExecutionResult(
                success=False,
                output="",
                error=f"Execution failed: {str(e)}",
                execution_time=0,
                warnings=["Unexpected error during secure execution"]
            )
        finally:
            # Cleanup sandbox directory
            if sandbox_dir and sandbox_dir.exists():
                try:
                    shutil.rmtree(sandbox_dir)
                except Exception as e:
                    logger.warning(f"Failed to cleanup sandbox directory: {e}")
    
    def _is_safe_code(self, code: str) -> bool:
        """Check if code is safe to execute."""
        # List of dangerous operations
        dangerous_patterns = [
            'import os', 'import sys', 'import subprocess', 'import shutil',
            'open(', 'file(', 'eval(', 'exec(', 'compile(',
            '__import__', 'getattr', 'setattr', 'delattr',
            'globals(', 'locals(', 'vars(', 'dir(',
            'input(', 'raw_input(',
        ]
        
        code_lower = code.lower()
        for pattern in dangerous_patterns:
            if pattern in code_lower:
                # Allow specific safe imports
                if pattern.startswith('import '):
                    module = pattern.split()[1]
                    if module not in self.allowed_imports:
                        return False
                else:
                    return False
        
        # Check for file operations
        if any(op in code for op in ['open(', 'file(', 'write(', 'delete']):
            return False
        
        return True


class CodeAnalysisTool(BaseTool):
    """Tool for analyzing Python code structure and quality."""
    
    name: str = "code_analysis"
    description: str = """
    Analyze Python code for structure, complexity, and quality.
    Use this to provide feedback on code examples and suggest improvements.
    
    Input: Python code string to analyze
    Output: Analysis result with structure info and suggestions
    """
    
    def _run(self, code: str) -> CodeAnalysisResult:
        """Analyze Python code."""
        try:
            # Parse the code
            tree = ast.parse(code)
            
            # Extract information
            imports = self._extract_imports(tree)
            functions = self._extract_functions(tree)
            classes = self._extract_classes(tree)
            complexity = self._calculate_complexity(tree)
            suggestions = self._generate_suggestions(tree, code)
            
            return CodeAnalysisResult(
                is_valid=True,
                complexity_score=complexity,
                suggestions=suggestions,
                imports=imports,
                functions=functions,
                classes=classes
            )
            
        except SyntaxError as e:
            return CodeAnalysisResult(
                is_valid=False,
                complexity_score=0,
                suggestions=[f"Syntax error: {str(e)}"],
                imports=[],
                functions=[],
                classes=[]
            )
        except Exception as e:
            return CodeAnalysisResult(
                is_valid=False,
                complexity_score=0,
                suggestions=[f"Analysis error: {str(e)}"],
                imports=[],
                functions=[],
                classes=[]
            )
    
    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """Extract import statements."""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}")
        return imports
    
    def _extract_functions(self, tree: ast.AST) -> List[str]:
        """Extract function definitions."""
        functions = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
        return functions
    
    def _extract_classes(self, tree: ast.AST) -> List[str]:
        """Extract class definitions."""
        classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
        return classes
    
    def _calculate_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity."""
        complexity = 1  # Base complexity
        
        for node in ast.walk(tree):
            # Decision points increase complexity
            if isinstance(node, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _generate_suggestions(self, tree: ast.AST, code: str) -> List[str]:
        """Generate improvement suggestions."""
        suggestions = []
        
        # Check for common issues
        lines = code.split('\n')
        
        # Long lines
        for i, line in enumerate(lines, 1):
            if len(line) > 88:  # PEP 8 recommends 79, but 88 is common
                suggestions.append(f"Line {i} is too long ({len(line)} chars). Consider breaking it up.")
        
        # Missing docstrings
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                if not ast.get_docstring(node):
                    suggestions.append(f"{node.__class__.__name__} '{node.name}' is missing a docstring.")
        
        # Too many arguments
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if len(node.args.args) > 5:
                    suggestions.append(f"Function '{node.name}' has too many arguments ({len(node.args.args)}). Consider using a class or reducing parameters.")
        
        return suggestions


class LintingTool(BaseTool):
    """Tool for code linting and style checking."""
    
    name: str = "code_linting"
    description: str = """
    Check Python code for style and common issues using basic linting rules.
    Use this to help users write cleaner, more maintainable code.
    
    Input: Python code string to lint
    Output: List of linting issues and suggestions
    """
    
    def _run(self, code: str) -> Dict[str, Any]:
        """Lint Python code."""
        issues = []
        
        try:
            # Parse the code first
            tree = ast.parse(code)
            lines = code.split('\n')
            
            # Check basic style issues
            for i, line in enumerate(lines, 1):
                line_issues = self._check_line(line, i)
                issues.extend(line_issues)
            
            # Check structural issues
            structural_issues = self._check_structure(tree)
            issues.extend(structural_issues)
            
            return {
                "total_issues": len(issues),
                "issues": issues,
                "status": "clean" if not issues else "has_issues"
            }
            
        except SyntaxError as e:
            return {
                "total_issues": 1,
                "issues": [f"Syntax error at line {e.lineno}: {e.msg}"],
                "status": "syntax_error"
            }
    
    def _check_line(self, line: str, line_num: int) -> List[str]:
        """Check a single line for issues."""
        issues = []
        
        # Trailing whitespace
        if line.endswith(' ') or line.endswith('\t'):
            issues.append(f"Line {line_num}: Trailing whitespace")
        
        # Line too long
        if len(line) > 88:
            issues.append(f"Line {line_num}: Line too long ({len(line)} chars)")
        
        # Multiple statements on one line
        if ';' in line and not line.strip().startswith('#'):
            issues.append(f"Line {line_num}: Multiple statements on one line")
        
        return issues
    
    def _check_structure(self, tree: ast.AST) -> List[str]:
        """Check structural issues."""
        issues = []
        
        # Unused variables (basic check)
        defined_vars = set()
        used_vars = set()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                if isinstance(node.ctx, ast.Store):
                    defined_vars.add(node.id)
                elif isinstance(node.ctx, ast.Load):
                    used_vars.add(node.id)
        
        unused = defined_vars - used_vars
        for var in unused:
            if not var.startswith('_'):  # Ignore variables starting with _
                issues.append(f"Variable '{var}' is defined but never used")
        
        return issues


class FormattingTool(BaseTool):
    """Tool for basic code formatting."""
    
    name: str = "code_formatting"
    description: str = """
    Format Python code to improve readability and follow basic style guidelines.
    Use this to help users learn proper code formatting.
    
    Input: Python code string to format
    Output: Formatted code with basic style improvements
    """
    
    def _run(self, code: str) -> Dict[str, Any]:
        """Format Python code."""
        try:
            # Basic formatting improvements
            lines = code.split('\n')
            formatted_lines = []
            
            for line in lines:
                # Remove trailing whitespace
                line = line.rstrip()
                
                # Basic indentation (convert tabs to spaces)
                line = line.expandtabs(4)
                
                formatted_lines.append(line)
            
            formatted_code = '\n'.join(formatted_lines)
            
            # Remove excessive blank lines
            formatted_code = self._remove_excessive_blank_lines(formatted_code)
            
            return {
                "formatted_code": formatted_code,
                "changes_made": self._get_changes(code, formatted_code),
                "status": "success"
            }
            
        except Exception as e:
            return {
                "formatted_code": code,
                "changes_made": [],
                "status": f"error: {str(e)}"
            }
    
    def _remove_excessive_blank_lines(self, code: str) -> str:
        """Remove excessive blank lines."""
        lines = code.split('\n')
        formatted_lines = []
        blank_count = 0
        
        for line in lines:
            if line.strip() == '':
                blank_count += 1
                if blank_count <= 2:  # Allow max 2 consecutive blank lines
                    formatted_lines.append(line)
            else:
                blank_count = 0
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    def _get_changes(self, original: str, formatted: str) -> List[str]:
        """Get list of changes made during formatting."""
        changes = []
        
        if original != formatted:
            changes.append("Removed trailing whitespace")
            changes.append("Standardized indentation")
            changes.append("Removed excessive blank lines")
        
        return changes 