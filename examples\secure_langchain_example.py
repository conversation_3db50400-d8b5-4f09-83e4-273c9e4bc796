"""
Secure LangChain Example - Demonstrating Safe AI Framework Usage
Generated with comprehensive security validation and error handling.

This example shows how to create a secure LangChain application with:
- Input validation and sanitization
- Proper error handling
- Resource management
- Rate limiting awareness
- Security best practices
"""

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from typing import Dict, Any, List, Optional
import logging
import asyncio
from datetime import datetime

# Configure logging for security monitoring
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SecureLangChainApp:
    """
    A secure LangChain application demonstrating best practices for:
    - Input validation
    - Error handling
    - Resource management
    - Security monitoring
    """
    
    def __init__(self):
        self.max_input_length = 1000
        self.max_output_length = 2000
        self.allowed_topics = {
            'programming', 'langchain', 'ai', 'python', 'development',
            'tutorial', 'example', 'documentation', 'help'
        }
        self.setup_chain()
    
    def setup_chain(self):
        """Set up the LangChain processing chain with security measures."""
        try:
            # Create a secure prompt template
            self.prompt = ChatPromptTemplate.from_messages([
                ("system", """You are a helpful AI assistant focused on programming and LangChain.
                
                SECURITY GUIDELINES:
                - Only provide information about programming, AI frameworks, and related topics
                - Do not execute or suggest dangerous code
                - Validate all inputs and outputs
                - Maintain user privacy and data security
                
                Current topic: {topic}
                """),
                ("human", "{user_input}")
            ])
            
            # Create output parser with validation
            self.output_parser = StrOutputParser()
            
            # Note: In a real application, you would add your LLM here
            # self.llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0.7)
            # self.chain = self.prompt | self.llm | self.output_parser
            
            logger.info("Secure LangChain application initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize LangChain application: {e}")
            raise
    
    def validate_input(self, user_input: str, topic: str = "general") -> Dict[str, Any]:
        """
        Validate user input for security and content appropriateness.
        
        Args:
            user_input: The user's input text
            topic: The topic category for the input
            
        Returns:
            Dict containing validation results
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "sanitized_input": user_input
        }
        
        try:
            # Check input length
            if len(user_input) > self.max_input_length:
                validation_result["valid"] = False
                validation_result["errors"].append(
                    f"Input too long: {len(user_input)} > {self.max_input_length}"
                )
            
            # Check for empty input
            if not user_input.strip():
                validation_result["valid"] = False
                validation_result["errors"].append("Input cannot be empty")
            
            # Basic content filtering
            dangerous_patterns = [
                'eval(', 'exec(', '__import__', 'subprocess', 'os.system',
                'open(', 'file(', 'input(', 'raw_input('
            ]
            
            for pattern in dangerous_patterns:
                if pattern in user_input.lower():
                    validation_result["valid"] = False
                    validation_result["errors"].append(
                        f"Potentially dangerous content detected: {pattern}"
                    )
            
            # Topic validation
            if topic and topic.lower() not in self.allowed_topics:
                validation_result["warnings"].append(
                    f"Topic '{topic}' may not be supported. Supported topics: {', '.join(self.allowed_topics)}"
                )
            
            # Sanitize input (remove control characters)
            import re
            sanitized = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', user_input)
            validation_result["sanitized_input"] = sanitized
            
            if sanitized != user_input:
                validation_result["warnings"].append("Input was sanitized to remove control characters")
            
        except Exception as e:
            logger.error(f"Input validation error: {e}")
            validation_result["valid"] = False
            validation_result["errors"].append(f"Validation error: {str(e)}")
        
        return validation_result
    
    def process_request(self, user_input: str, topic: str = "programming") -> Dict[str, Any]:
        """
        Process a user request with full security validation.
        
        Args:
            user_input: The user's input text
            topic: The topic category
            
        Returns:
            Dict containing the response and metadata
        """
        start_time = datetime.now()
        
        try:
            # Validate input
            validation = self.validate_input(user_input, topic)
            
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": "Input validation failed",
                    "details": validation["errors"],
                    "warnings": validation["warnings"],
                    "processing_time": (datetime.now() - start_time).total_seconds()
                }
            
            # Log the request (without sensitive data)
            logger.info(f"Processing request - Topic: {topic}, Length: {len(user_input)}")
            
            # In a real application, you would process with your LLM chain here
            # For this example, we'll simulate a response
            simulated_response = self._generate_safe_response(validation["sanitized_input"], topic)
            
            # Validate output
            output_validation = self._validate_output(simulated_response)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": True,
                "response": simulated_response,
                "topic": topic,
                "input_validation": validation,
                "output_validation": output_validation,
                "processing_time": processing_time,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Request processing error: {e}")
            return {
                "success": False,
                "error": f"Processing failed: {str(e)}",
                "processing_time": (datetime.now() - start_time).total_seconds()
            }
    
    def _generate_safe_response(self, user_input: str, topic: str) -> str:
        """Generate a safe response based on the input and topic."""
        
        # This is a simulation - in a real app, this would use your LLM chain
        response_templates = {
            "langchain": f"""
Here's information about LangChain related to your query: "{user_input[:100]}..."

LangChain is a framework for developing applications powered by language models. Key concepts include:

1. **Chains**: Sequences of calls to components like LLMs, tools, or data preprocessing steps
2. **Prompts**: Templates for formatting input to language models
3. **Memory**: Components for persisting state between chain calls
4. **Agents**: Systems that use LLMs to decide which actions to take

For your specific question about "{user_input[:50]}...", I recommend:
- Checking the official LangChain documentation
- Looking at community examples and tutorials
- Starting with simple chains before building complex applications

Remember to always validate inputs and handle errors appropriately in your LangChain applications.
""",
            "programming": f"""
Regarding your programming question: "{user_input[:100]}..."

Here are some general programming best practices:

1. **Security**: Always validate and sanitize inputs
2. **Error Handling**: Use try-catch blocks and proper error reporting
3. **Code Quality**: Write clean, readable, and maintainable code
4. **Testing**: Implement comprehensive testing strategies
5. **Documentation**: Document your code and APIs clearly

For specific programming questions, please provide more context about:
- The programming language you're using
- The specific problem you're trying to solve
- Any error messages you're encountering

This will help provide more targeted assistance.
""",
            "general": f"""
Thank you for your question: "{user_input[:100]}..."

I'm designed to help with programming, AI frameworks (especially LangChain), and related technical topics. 

If you have questions about:
- LangChain framework usage
- Python programming
- AI application development
- Code examples and tutorials

Please feel free to ask, and I'll provide helpful, secure guidance.

For the best assistance, please specify:
- What you're trying to accomplish
- What framework or language you're using
- Any specific challenges you're facing
"""
        }
        
        # Select appropriate template
        if "langchain" in user_input.lower():
            template = response_templates["langchain"]
        elif any(term in user_input.lower() for term in ["code", "program", "function", "class"]):
            template = response_templates["programming"]
        else:
            template = response_templates["general"]
        
        return template.strip()
    
    def _validate_output(self, output: str) -> Dict[str, Any]:
        """Validate the generated output for safety and appropriateness."""
        validation = {
            "valid": True,
            "warnings": [],
            "length": len(output)
        }
        
        # Check output length
        if len(output) > self.max_output_length:
            validation["warnings"].append(f"Output length ({len(output)}) exceeds recommended maximum ({self.max_output_length})")
        
        # Check for potentially sensitive information (basic check)
        sensitive_patterns = ['password', 'api_key', 'secret', 'token']
        for pattern in sensitive_patterns:
            if pattern in output.lower():
                validation["warnings"].append(f"Output may contain sensitive information: {pattern}")
        
        return validation


def main():
    """Demonstrate the secure LangChain application."""
    print("🔒 Secure LangChain Example Application")
    print("=" * 50)
    
    # Initialize the secure application
    app = SecureLangChainApp()
    
    # Example queries to demonstrate security features
    test_queries = [
        {
            "input": "How do I create a simple LangChain chain?",
            "topic": "langchain"
        },
        {
            "input": "What are Python best practices for error handling?",
            "topic": "programming"
        },
        {
            "input": "eval('print(\"dangerous code\")')",  # This should be blocked
            "topic": "programming"
        },
        {
            "input": "A" * 1500,  # This should be blocked for length
            "topic": "general"
        }
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test Query {i}:")
        print(f"Input: {query['input'][:100]}{'...' if len(query['input']) > 100 else ''}")
        print(f"Topic: {query['topic']}")
        
        # Process the query
        result = app.process_request(query["input"], query["topic"])
        
        print(f"✅ Success: {result['success']}")
        
        if result["success"]:
            print(f"📄 Response: {result['response'][:200]}{'...' if len(result['response']) > 200 else ''}")
            print(f"⏱️  Processing Time: {result['processing_time']:.3f}s")
            
            if result["input_validation"]["warnings"]:
                print(f"⚠️  Warnings: {', '.join(result['input_validation']['warnings'])}")
        else:
            print(f"❌ Error: {result['error']}")
            if "details" in result:
                print(f"📋 Details: {', '.join(result['details'])}")
        
        print("-" * 50)
    
    print("\n🎯 Security Features Demonstrated:")
    print("✓ Input validation and sanitization")
    print("✓ Content filtering for dangerous patterns")
    print("✓ Length limits and resource management")
    print("✓ Comprehensive error handling")
    print("✓ Security logging and monitoring")
    print("✓ Output validation")
    
    print("\n📚 This example shows how to build secure AI applications with:")
    print("- Proper input validation")
    print("- Error handling and recovery")
    print("- Security monitoring")
    print("- Resource management")
    print("- Safe content generation")


if __name__ == "__main__":
    main()
