"""
Security utilities for input validation, sanitization, and safe parameter extraction.
Provides comprehensive protection against code injection and malicious input.
"""

import re
import html
import os
import ast
import keyword
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path
import logging
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)


class SecurityLevel(Enum):
    """Security levels for different operations."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


class InputSanitizer:
    """Comprehensive input sanitization and validation."""
    
    # Dangerous patterns that should be blocked
    DANGEROUS_PATTERNS = [
        r'__import__\s*\(',
        r'eval\s*\(',
        r'exec\s*\(',
        r'compile\s*\(',
        r'open\s*\(',
        r'file\s*\(',
        r'input\s*\(',
        r'raw_input\s*\(',
        r'subprocess\.',
        r'os\.',
        r'sys\.',
        r'shutil\.',
        r'pickle\.',
        r'marshal\.',
        r'imp\.',
        r'importlib\.',
        r'globals\s*\(',
        r'locals\s*\(',
        r'vars\s*\(',
        r'dir\s*\(',
        r'getattr\s*\(',
        r'setattr\s*\(',
        r'delattr\s*\(',
        r'hasattr\s*\(',
        r'callable\s*\(',
        r'classmethod\s*\(',
        r'staticmethod\s*\(',
        r'property\s*\(',
        r'super\s*\(',
        r'type\s*\(',
        r'isinstance\s*\(',
        r'issubclass\s*\(',
        r'help\s*\(',
        r'quit\s*\(',
        r'exit\s*\(',
        r'copyright\s*\(',
        r'license\s*\(',
        r'credits\s*\(',
    ]
    
    # Safe imports for code generation
    SAFE_IMPORTS = {
        'langchain', 'langchain_core', 'langchain_openai', 'langchain_anthropic',
        'langchain_google_genai', 'langgraph', 'pydantic', 'typing', 'datetime',
        'json', 'math', 'random', 'string', 'collections', 'itertools', 'functools',
        'dataclasses', 'enum', 'uuid', 'time', 'asyncio', 'logging'
    }
    
    # Maximum lengths for different input types
    MAX_LENGTHS = {
        'filename': 255,
        'content': 50000,  # 50KB max for code content
        'description': 1000,
        'query': 500,
        'framework': 50,
        'language': 20,
        'user_message': 10000
    }
    
    @classmethod
    def sanitize_filename(cls, filename: str, max_length: int = 255) -> str:
        """Sanitize filename to prevent path traversal and invalid characters."""
        if not filename:
            raise ValidationError("Filename cannot be empty")
        
        # Remove path separators and dangerous characters
        filename = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', filename)
        
        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')
        
        # Ensure it doesn't start with a dot (hidden file)
        if filename.startswith('.'):
            filename = 'file_' + filename[1:]
        
        # Truncate if too long
        if len(filename) > max_length:
            name, ext = os.path.splitext(filename)
            filename = name[:max_length-len(ext)-1] + ext
        
        # Ensure it has a valid extension
        if not filename.endswith(('.py', '.txt', '.md', '.json', '.yaml', '.yml')):
            filename += '.py'
        
        return filename
    
    @classmethod
    def sanitize_code_content(cls, content: str, security_level: SecurityLevel = SecurityLevel.HIGH) -> str:
        """Sanitize code content to prevent dangerous operations."""
        if not content:
            return ""
        
        # Check length
        if len(content) > cls.MAX_LENGTHS['content']:
            raise ValidationError(f"Content too long: {len(content)} > {cls.MAX_LENGTHS['content']}")
        
        # HTML escape to prevent XSS
        content = html.escape(content)
        content = html.unescape(content)  # Unescape to keep code readable
        
        if security_level in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]:
            # Check for dangerous patterns
            for pattern in cls.DANGEROUS_PATTERNS:
                if re.search(pattern, content, re.IGNORECASE):
                    logger.warning(f"Blocked dangerous pattern: {pattern}")
                    raise ValidationError(f"Content contains potentially dangerous code pattern")
        
        # Validate Python syntax if it looks like Python code
        if cls._looks_like_python(content):
            try:
                ast.parse(content)
            except SyntaxError as e:
                logger.info(f"Content has syntax errors, will generate safe alternative: {e}")
                # Don't raise error, just log - we'll generate safe content instead
        
        return content
    
    @classmethod
    def _looks_like_python(cls, content: str) -> bool:
        """Check if content looks like Python code."""
        python_indicators = [
            'import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while ',
            'try:', 'except:', 'with ', 'async ', 'await ', 'lambda ',
            'print(', 'return ', 'yield ', 'raise ', 'assert '
        ]
        return any(indicator in content for indicator in python_indicators)
    
    @classmethod
    def validate_imports(cls, content: str) -> List[str]:
        """Extract and validate imports from code content."""
        try:
            tree = ast.parse(content)
        except SyntaxError:
            return []
        
        imports = []
        unsafe_imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    module_name = alias.name.split('.')[0]
                    if module_name not in cls.SAFE_IMPORTS:
                        unsafe_imports.append(module_name)
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    module_name = node.module.split('.')[0]
                    if module_name not in cls.SAFE_IMPORTS:
                        unsafe_imports.append(module_name)
                    imports.append(node.module)
        
        if unsafe_imports:
            logger.warning(f"Unsafe imports detected: {unsafe_imports}")
            raise ValidationError(f"Unsafe imports not allowed: {', '.join(unsafe_imports)}")
        
        return imports
    
    @classmethod
    def sanitize_user_message(cls, message: str) -> str:
        """Sanitize user message input."""
        if not message:
            return ""
        
        # Check length
        if len(message) > cls.MAX_LENGTHS['user_message']:
            message = message[:cls.MAX_LENGTHS['user_message']]
        
        # Remove null bytes and control characters
        message = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', message)
        
        # HTML escape for safety
        message = html.escape(message)
        message = html.unescape(message)  # Keep readable
        
        return message.strip()
    
    @classmethod
    def sanitize_framework_name(cls, framework: str) -> str:
        """Sanitize framework name."""
        if not framework:
            raise ValidationError("Framework name cannot be empty")
        
        # Only allow alphanumeric, underscore, hyphen
        framework = re.sub(r'[^a-zA-Z0-9_-]', '', framework.lower())
        
        if len(framework) > cls.MAX_LENGTHS['framework']:
            framework = framework[:cls.MAX_LENGTHS['framework']]
        
        # Validate against known frameworks
        valid_frameworks = {'langchain', 'langgraph', 'crewai', 'autogen', 'llamaindex'}
        if framework not in valid_frameworks:
            logger.warning(f"Unknown framework: {framework}, defaulting to langchain")
            framework = 'langchain'
        
        return framework


class ParameterValidator:
    """Validates and extracts safe parameters for tool execution."""

    def __init__(self, security_level: SecurityLevel = SecurityLevel.HIGH):
        self.security_level = security_level
        self.sanitizer = InputSanitizer()

    def extract_safe_parameters(self, tool_name: str, user_message: str,
                               state: Any = None) -> Dict[str, Any]:
        """Extract safe parameters for tool execution."""
        try:
            # Sanitize user message first
            safe_message = self.sanitizer.sanitize_user_message(user_message)

            if tool_name == "create_code_file":
                return self._extract_code_file_params(safe_message, state)
            elif tool_name == "auto_create_files_from_text":
                return self._extract_auto_create_params(safe_message, state)
            elif tool_name == "start_practice_session":
                return self._extract_practice_session_params(safe_message, state)
            elif tool_name in ["tavily_search", "search_framework_documentation"]:
                return self._extract_search_params(safe_message, state)
            elif tool_name == "execute_practice_file":
                return self._extract_execution_params(safe_message, state)
            else:
                # Generic parameter extraction
                return self._extract_generic_params(tool_name, safe_message, state)

        except ValidationError as e:
            logger.error(f"Parameter validation failed for {tool_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in parameter extraction for {tool_name}: {e}")
            raise ValidationError(f"Failed to extract safe parameters: {str(e)}")

    def _extract_code_file_params(self, message: str, state: Any) -> Dict[str, Any]:
        """Extract parameters for code file creation."""
        # Generate safe filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"example_{timestamp}.py"
        filename = self.sanitizer.sanitize_filename(filename)

        # Determine if we should use the message as content or generate safe content
        content = self._generate_safe_code_content(message, state)

        return {
            "content": content,
            "filename": filename,
            "language": "python"
        }

    def _extract_auto_create_params(self, message: str, state: Any) -> Dict[str, Any]:
        """Extract parameters for auto file creation."""
        framework = getattr(state, 'framework', None)
        framework_name = framework.value if framework else 'langchain'
        framework_name = self.sanitizer.sanitize_framework_name(framework_name)

        return {
            "text": message,
            "context": f"Framework: {framework_name}"
        }

    def _extract_practice_session_params(self, message: str, state: Any) -> Dict[str, Any]:
        """Extract parameters for practice session."""
        framework = getattr(state, 'framework', None)
        framework_name = framework.value if framework else 'langchain'
        framework_name = self.sanitizer.sanitize_framework_name(framework_name)

        user_id = getattr(state, 'user_id', 'anonymous')
        # Sanitize user_id
        user_id = re.sub(r'[^a-zA-Z0-9_-]', '', str(user_id))

        return {
            "framework": framework_name,
            "user_id": user_id,
            "task_description": message
        }

    def _extract_search_params(self, message: str, state: Any) -> Dict[str, Any]:
        """Extract parameters for search operations."""
        # Limit query length and sanitize
        query = message[:500]  # Limit search query length
        query = re.sub(r'[^\w\s\-\+\.]', '', query)  # Only allow safe characters

        return {
            "query": query,
            "max_results": 5  # Limit results to prevent abuse
        }

    def _extract_execution_params(self, message: str, state: Any) -> Dict[str, Any]:
        """Extract parameters for code execution."""
        # For execution, we need to be very careful
        return {
            "timeout": 30,  # Always limit execution time
            "memory_limit": "128MB",  # Limit memory usage
            "safe_mode": True
        }

    def _extract_generic_params(self, tool_name: str, message: str, state: Any) -> Dict[str, Any]:
        """Extract generic parameters for unknown tools."""
        return {
            "input": message,
            "safe_mode": True,
            "max_length": 1000
        }

    def _generate_safe_code_content(self, user_message: str, state: Any) -> str:
        """Generate safe code content based on user message and context."""
        framework = getattr(state, 'framework', None)
        framework_name = framework.value if framework else 'langchain'

        # Try to sanitize user input first
        try:
            sanitized_content = self.sanitizer.sanitize_code_content(user_message)
            # If user provided actual code, validate it
            if self.sanitizer._looks_like_python(sanitized_content):
                self.sanitizer.validate_imports(sanitized_content)
                return sanitized_content
        except ValidationError:
            # If user input is unsafe, generate safe alternative
            pass

        # Generate safe code based on framework and user intent
        return self._generate_framework_example(framework_name, user_message)

    def _generate_framework_example(self, framework: str, user_intent: str) -> str:
        """Generate safe framework-specific code examples."""
        templates = {
            'langchain': '''"""
{intent} - LangChain Example
Generated safely based on your request: {intent}
"""

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

def create_simple_chain():
    """Create a simple LangChain chain for: {intent}"""

    # Create a prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a helpful AI assistant for {intent}."),
        ("human", "{{user_input}}")
    ])

    # Create output parser
    output_parser = StrOutputParser()

    # This is a template - you'll need to add your LLM
    # chain = prompt | llm | output_parser

    print("LangChain chain template created successfully!")
    print("Intent: {intent}")
    return prompt

if __name__ == "__main__":
    chain = create_simple_chain()
    print("Example completed successfully!")
''',
            'langgraph': '''"""
{intent} - LangGraph Example
Generated safely based on your request: {intent}
"""

from typing import Dict, Any
from langgraph.graph import StateGraph, END
from pydantic import BaseModel

class GraphState(BaseModel):
    """State for the LangGraph example."""
    messages: list = []
    current_step: str = "start"
    intent: str = "{intent}"

def create_simple_graph():
    """Create a simple LangGraph for: {intent}"""

    def start_node(state: GraphState) -> Dict[str, Any]:
        """Starting node of the graph."""
        return {{
            "messages": state.messages + [f"Starting graph execution for: {{state.intent}}"],
            "current_step": "processing"
        }}

    def process_node(state: GraphState) -> Dict[str, Any]:
        """Processing node of the graph."""
        return {{
            "messages": state.messages + [f"Processing complete for: {{state.intent}}"],
            "current_step": "end"
        }}

    # Create the graph
    workflow = StateGraph(GraphState)

    # Add nodes
    workflow.add_node("start", start_node)
    workflow.add_node("process", process_node)

    # Add edges
    workflow.add_edge("start", "process")
    workflow.add_edge("process", END)

    # Set entry point
    workflow.set_entry_point("start")

    print("LangGraph created successfully!")
    print("Intent: {intent}")
    return workflow

if __name__ == "__main__":
    graph = create_simple_graph()
    print("Example completed successfully!")
'''
        }

        # Add more templates for other frameworks
        templates['crewai'] = '''"""
{intent} - CrewAI Example
Generated safely based on your request: {intent}
"""

def create_simple_crew():
    """Create a simple CrewAI crew for: {intent}"""

    # This is a template structure for CrewAI
    crew_config = {{
        "name": "Example Crew for {intent}",
        "description": "A simple crew for {intent}",
        "agents": [
            {{
                "name": "Researcher",
                "role": "Research Specialist",
                "goal": "Gather information for {intent}"
            }},
            {{
                "name": "Writer",
                "role": "Content Creator",
                "goal": "Create clear content for {intent}"
            }}
        ],
        "tasks": [
            {{
                "description": "Research: {intent}",
                "agent": "Researcher"
            }},
            {{
                "description": "Write summary for: {intent}",
                "agent": "Writer"
            }}
        ]
    }}

    print("CrewAI configuration created successfully!")
    print("Intent: {intent}")
    return crew_config

if __name__ == "__main__":
    crew = create_simple_crew()
    print("Example completed successfully!")
'''

        templates['autogen'] = '''"""
{intent} - AutoGen Example
Generated safely based on your request: {intent}
"""

def create_simple_autogen_setup():
    """Create a simple AutoGen setup for: {intent}"""

    # This is a template structure for AutoGen
    agent_config = {{
        "agents": [
            {{
                "name": "assistant",
                "type": "AssistantAgent",
                "system_message": f"You are a helpful AI assistant for: {intent}"
            }},
            {{
                "name": "user_proxy",
                "type": "UserProxyAgent",
                "human_input_mode": "NEVER",
                "code_execution_config": False
            }}
        ],
        "conversation_config": {{
            "max_consecutive_auto_reply": 3,
            "human_input_mode": "NEVER"
        }},
        "intent": "{intent}"
    }}

    print("AutoGen configuration created successfully!")
    print("Intent: {intent}")
    return agent_config

if __name__ == "__main__":
    config = create_simple_autogen_setup()
    print("Example completed successfully!")
'''

        template = templates.get(framework, templates['langchain'])
        # Limit intent length and sanitize
        safe_intent = re.sub(r'[^\w\s\-\.]', '', user_intent[:100])
        return template.format(intent=safe_intent)
