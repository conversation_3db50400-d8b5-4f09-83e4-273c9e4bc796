"""
Base agent class for PyFrameworks Assistant.
Provides common functionality for all agent types.
"""

import os
import re
import json
import logging
import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set, Union

from langchain.tools import BaseTool
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, BaseMessage
from langchain_core.language_models.chat_models import BaseChatModel
from pydantic import BaseModel, Field

from ..core.constellation_types import AgentRole
from ..core.constellation import ConstellationState
from ..config.settings import Settings
from ..core.models import llm_manager, initialize_chat_model, get_default_model
from ..core.security import ParameterValidator, ValidationError, SecurityLevel
from ..core.circuit_breaker import tool_execution_manager, CircuitBreakerOpenError

# Initialize settings
settings = Settings()

logger = logging.getLogger(__name__)


class AgentResponse(BaseModel):
    """Response from an agent."""
    content: str = Field(description="Agent response content")
    agent_role: AgentRole = Field(description="Role of the responding agent")
    confidence_score: float = Field(default=0.8, description="Confidence in response")
    suggested_handoff: Optional[AgentRole] = Field(default=None, description="Suggested next agent")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class BaseAgent(ABC):
    """Base class for all specialized agents in the constellation."""
    
    def __init__(
        self, 
        role: AgentRole, 
        model: Optional[BaseChatModel] = None,
        specialization: str = "general",
        tools: Optional[List[BaseTool]] = None
    ):
        """Initialize the base agent."""
        self.role = role
        # Ensure llm_manager is properly initialized and use it to get the default model
        try:
            self.model = model or llm_manager.get_default_model()
        except (AttributeError, ValueError):
            # Fallback to importing and initializing the model directly if llm_manager fails
            try:
                self.model = model or get_default_model()
                logger.info("Using get_default_model() for agent initialization")
            except Exception:
                self.model = model or initialize_chat_model()
                logger.warning("Using fallback model initialization in BaseAgent")
            
        self.specialization = specialization
        self.tools = tools or []
        self.activation_count = 0
        self.success_rate = 0.0
        self.last_activation = None

        # Initialize security components
        self.parameter_validator = ParameterValidator(SecurityLevel.HIGH)
        
    @abstractmethod
    def get_system_prompt(self, state: ConstellationState) -> str:
        """Get the system prompt for this agent."""
        pass
    
    @abstractmethod
    def should_activate(self, state: ConstellationState) -> bool:
        """Determine if this agent should activate for the current state."""
        pass
    
    def get_tools_description(self) -> str:
        """Get description of available tools for the system prompt."""
        if not self.tools:
            return ""
        
        tools_desc = "\n\nAvailable Tools:\n"
        for tool in self.tools:
            tools_desc += f"- {tool.name}: {tool.description}\n"
        
        tools_desc += "\nYou can use these tools when appropriate to provide better assistance."
        return tools_desc
    
    async def process(self, state: ConstellationState) -> AgentResponse:
        """Process the current state and generate a response."""
        try:
            self.activation_count += 1
            self.last_activation = datetime.now()
            
            # Get the latest user message
            user_messages = [msg for msg in state.messages if isinstance(msg, HumanMessage)]
            if not user_messages:
                return AgentResponse(
                    content="I need a question or input to help you with.",
                    agent_role=self.role,
                    confidence_score=0.3
                )
            
            latest_message = user_messages[-1]
            
            # Check if agent should use tools based on user intent
            should_use_tools = self._should_use_tools(latest_message.content, state)
            
            if should_use_tools and self.tools:
                # Use tools-enabled approach
                return await self._process_with_tools(state)
            else:
                # Use standard approach
                return await self._process_standard(state)
                
        except Exception as e:
            logger.error(f"Error in {self.role.value} agent processing: {e}")
            return AgentResponse(
                content=f"I encountered an error while processing your request: {str(e)}. Please try rephrasing your question.",
                agent_role=self.role,
                confidence_score=0.2,
                metadata={"error": str(e)}
            )
    
    def _should_use_tools(self, message: str, state: ConstellationState) -> bool:
        """Determine if the agent should use tools for this request."""
        message_lower = message.lower()
        
        # Tools usage keywords for different agents
        if self.role == AgentRole.CODE_ASSISTANT:
            # Be more aggressive about using tools for Code Assistant
            coding_keywords = [
                "create", "build", "code", "example", "practice", "file", "implement", "write",
                "ready", "start", "hands-on", "try", "exercise", "session", "demo", "tutorial",
                "show me", "let's", "want to", "i want", "can you", "help me"
            ]
            
            # Also check for explicit practice/coding intentions
            practice_phrases = [
                "practice with code", "want to practice", "start with code", 
                "i'm ready", "yeah ready", "hands-on", "code first", "let's practice"
            ]
            
            # Use tools if ANY coding keyword is found OR if it's an explicit practice phrase
            has_coding_keyword = any(word in message_lower for word in coding_keywords)
            has_practice_phrase = any(phrase in message_lower for phrase in practice_phrases)
            
            # For Code Assistant, be more liberal with tool usage
            return has_coding_keyword or has_practice_phrase or len(message_lower.strip()) < 20
            
        elif self.role == AgentRole.PRACTICE_FACILITATOR:
            return any(word in message_lower for word in [
                "practice", "exercise", "session", "hands-on", "try", "learn"
            ])
        elif self.role == AgentRole.RESEARCH_ASSISTANT:
            return any(word in message_lower for word in [
                "search", "find", "latest", "research", "current", "new"
            ])
        
        return False
    
    async def _process_with_tools(self, state: ConstellationState) -> AgentResponse:
        """Process with tools enabled for function calling."""
        from ..core.action_display import action_display
        
        try:
            # Get the latest user message
            latest_message = state.messages[-1]
            
            # Generate the system prompt for tool usage
            system_prompt = self.get_system_prompt(state)
            system_prompt += self.get_tools_description()
            system_prompt += "\n\nIMPORTANT: When the user requests hands-on practice or code creation, USE the available tools to actually create files and execute code. Don't just explain - DO!"
            
            # Execute tools based on agent type and user request
            try:
                tool_results = await self._execute_relevant_tools(latest_message.content, state)
            except Exception as e:
                logger.error(f"Tool execution error: {e}")
                # Gracefully handle tool execution failures
                tool_results = [{"tool": "error", "error": f"Tool execution failed: {str(e)}"}]
            
            # Generate response incorporating tool results
            try:
                response_content = await self._generate_response_with_tool_results(
                    system_prompt, state, tool_results
                )
            except Exception as e:
                logger.error(f"Response generation error: {e}")
                # Fallback to a simple response if generation fails
                response_content = self._generate_fallback_response(tool_results)
            
            # Analyze for potential handoffs
            suggested_handoff = self._analyze_for_handoff(response_content, state)
            
            # Calculate confidence score
            confidence = self._calculate_confidence(response_content)
            
            return AgentResponse(
                content=response_content,
                agent_role=self.role,
                confidence_score=confidence,
                suggested_handoff=suggested_handoff,
                metadata={
                    "activation_count": self.activation_count,
                    "tools_used": len(tool_results)
                }
            )
        except Exception as e:
            # Catch-all error handler for any unexpected issues
            logger.error(f"Unexpected error in _process_with_tools: {e}")
            return AgentResponse(
                content=f"I encountered an issue while processing your request. {str(e)}. Let me try to answer more directly.",
                agent_role=self.role,
                confidence_score=0.3,
                metadata={"error": str(e)}
            )
    
    async def _process_standard(self, state: ConstellationState) -> AgentResponse:
        """Process using standard approach without tools."""
        # Generate the system prompt for response
        system_prompt = self.get_system_prompt(state)
        
        # Generate conversation for response
        conversation = [
            SystemMessage(content=system_prompt),
            *state.messages[-6:],  # Last 6 messages for context
        ]
        
        # Generate response
        response = await self.model.ainvoke(conversation)
        
        # Analyze for potential handoffs
        suggested_handoff = self._analyze_for_handoff(response.content, state)
        
        # Calculate confidence score
        confidence = self._calculate_confidence(response.content)
        
        return AgentResponse(
            content=response.content,
            agent_role=self.role,
            confidence_score=confidence,
            suggested_handoff=suggested_handoff,
            metadata={
                "activation_count": self.activation_count
            }
        )
    
    async def _execute_relevant_tools(self, user_message: str, state: ConstellationState) -> List[Dict[str, Any]]:
        """Execute relevant tools based on user message and agent type."""
        from ..core.action_display import action_display
        
        tool_results = []
        message_lower = user_message.lower()
        
        # ENHANCEMENT: Execute all appropriate tools based on message content
        # Rather than just suggesting tools, we actually execute them now
        for tool in self.tools:
            if self._should_execute_tool(tool, user_message, state):
                try:
                    action_id = action_display.show_tool_execution_start(
                        tool.name, 
                        {"query": user_message[:100], "framework": state.framework.value}
                    )
                    
                    # CRITICAL CHANGE: Actually execute the tool
                    result = await self._execute_single_tool(tool, user_message, state)
                    
                    action_display.show_action_success(action_id, f"Tool {tool.name} executed", {"result_length": len(str(result))})
                    tool_results.append({"tool": tool.name, "result": result})
                    
                except Exception as e:
                    logger.error(f"Tool execution error: {e}")
                    action_display.show_action_error(action_id, f"Tool execution failed: {tool.name}", str(e))
                    tool_results.append({"tool": tool.name, "error": str(e)})
        
        # If no tools were executed but we should use tools, use default tools based on agent role
        if not tool_results and self.tools:
            if self.role == AgentRole.RESEARCH_ASSISTANT:
                # Research Assistant should search for information by default
                search_tool = next((t for t in self.tools if t.name == "tavily_search"), None)
                if search_tool:
                    try:
                        action_id = action_display.show_tool_execution_start(
                            search_tool.name, 
                            {"query": user_message[:100]}
                        )
                        result = await self._execute_single_tool(search_tool, user_message, state)
                        action_display.show_action_success(action_id, "Search completed", {"result_length": len(str(result))})
                        tool_results.append({"tool": search_tool.name, "result": result})
                    except Exception as e:
                        logger.error(f"Search tool execution error: {e}")
                        action_display.show_action_error(action_id, "Search failed", str(e))
                    
            elif self.role == AgentRole.CODE_ASSISTANT:
                # Code Assistant should start practice by default
                for tool in self.tools:
                    if tool.name == "start_practice_session":
                        try:
                            action_id = action_display.show_tool_execution_start(
                                tool.name, 
                                {"user_id": "user_001", "framework": state.framework.value, "task": user_message[:100]}
                            )
                            
                            result = tool.invoke({
                                "user_id": "user_001",
                                "framework": state.framework.value, 
                                "task_description": f"User request: {user_message}"
                            })
                            
                            action_display.show_action_success(action_id, "Practice session started", {"session": result})
                            tool_results.append({"tool": tool.name, "result": result})
                            
                            # Extract session ID for follow-up actions
                            session_id = result.split("Session ID: ")[1].split("\n")[0] if "Session ID:" in result else None
                            
                            # Continue with existing practice file creation code...
                            if session_id:
                                create_tool = next((t for t in self.tools if t.name == "create_practice_file_with_auto_deps"), None)
                                if create_tool:
                                    action_id = action_display.show_tool_execution_start(
                                        create_tool.name,
                                        {"session_id": session_id, "filename": "basic_example.py"}
                                    )
                                    
                                    # Generate dynamic code based on framework and user request
                                    # First try to use the CodeAssistantAgent's dynamic code generation if available
                                    try:
                                        from .code_assistant_agent import CodeAssistantAgent
                                        code_assistant = CodeAssistantAgent()
                                        code = code_assistant.generate_dynamic_code(
                                            user_message=user_message,
                                            framework=state.framework.value,
                                            skill_level=state.user_preferences.get('skill_level', 'intermediate')
                                        )
                                    except (ImportError, AttributeError):
                                        # Fall back to the template-based generation if code assistant is not available
                                        code = self._generate_framework_code(state.framework.value, user_message)
                                    
                                    create_result = create_tool.invoke({
                                        "session_id": session_id,
                                        "filename": "basic_example.py",
                                        "code": code
                                    })
                                    
                                    action_display.show_action_success(action_id, "Practice file created", {"file": "basic_example.py"})
                                    tool_results.append({"tool": create_tool.name, "result": create_result})
                                    
                                    # Execute the practice file
                                    execute_tool = next((t for t in self.tools if t.name == "execute_practice_file"), None)
                                    if execute_tool:
                                        action_id = action_display.show_tool_execution_start(
                                            execute_tool.name,
                                            {"session_id": session_id, "filename": "basic_example.py"}
                                        )
                                        
                                        execute_result = execute_tool.invoke({
                                            "session_id": session_id,
                                            "filename": "basic_example.py"
                                        })
                                        
                                        action_display.show_action_success(action_id, "Code executed successfully", {"output": execute_result[:200]})
                                        tool_results.append({"tool": execute_tool.name, "result": execute_result})
                        
                        except Exception as e:
                            action_display.show_action_error(action_id, f"Tool execution failed: {tool.name}", str(e))
                            logger.error(f"Tool execution error: {e}")
            
        return tool_results
    
    async def _execute_single_tool(self, tool: BaseTool, user_message: str, state: ConstellationState) -> Dict[str, Any]:
        """Execute a single tool with circuit breaker protection and comprehensive error handling."""
        try:
            # Extract parameters from the message based on tool requirements
            params = self._extract_tool_parameters(tool, user_message, state)

            # Log the tool execution attempt
            logger.info(f"Executing tool: {tool.name} with params: {params}")

            # Execute the tool with circuit breaker protection
            async def tool_execution():
                start_time = datetime.now()
                if asyncio.iscoroutinefunction(tool.invoke):
                    result = await tool.invoke(params)
                else:
                    result = tool.invoke(params)
                execution_time = (datetime.now() - start_time).total_seconds()
                return result, execution_time

            result, execution_time = await tool_execution_manager.execute_tool_safely(
                tool.name, tool_execution
            )

            # Log successful execution
            logger.info(f"Tool {tool.name} executed successfully in {execution_time:.2f}s")

            # Add tool execution to state context
            if "tool_executions" not in state.session_context:
                state.session_context["tool_executions"] = []

            state.session_context["tool_executions"].append({
                "tool": tool.name,
                "parameters": params,
                "success": True,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            })

            return result
        except CircuitBreakerOpenError as e:
            # Handle circuit breaker open state
            logger.error(f"Circuit breaker open for tool {tool.name}: {e}")
            # Add failed execution to state context
            if "tool_executions" not in state.session_context:
                state.session_context["tool_executions"] = []

            state.session_context["tool_executions"].append({
                "tool": tool.name,
                "parameters": {},
                "success": False,
                "error": f"Service temporarily unavailable: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
            return {"error": f"Service temporarily unavailable: {tool.name} is experiencing issues. Please try again later.", "details": str(e)}

        except ValidationError as e:
            # Handle parameter validation errors
            logger.error(f"Parameter validation failed for tool {tool.name}: {e}")
            # Add failed execution to state context
            if "tool_executions" not in state.session_context:
                state.session_context["tool_executions"] = []

            state.session_context["tool_executions"].append({
                "tool": tool.name,
                "parameters": {},
                "success": False,
                "error": f"Invalid input: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
            return {"error": f"Invalid input for {tool.name}: {str(e)}", "details": str(e)}

        except ConnectionError as e:
            # Handle Redis connection errors specifically
            logger.error(f"Redis connection error in tool execution: {e}")
            # Add failed execution to state context
            if "tool_executions" not in state.session_context:
                state.session_context["tool_executions"] = []

            state.session_context["tool_executions"].append({
                "tool": tool.name,
                "parameters": {},
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return {"error": "Database connection error. Continuing with limited functionality.", "details": str(e)}

        except Exception as e:
            # Handle other errors
            logger.error(f"Error executing tool {tool.name}: {e}")
            # Add failed execution to state context
            if "tool_executions" not in state.session_context:
                state.session_context["tool_executions"] = []

            state.session_context["tool_executions"].append({
                "tool": tool.name,
                "parameters": {},
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return {"error": f"Error executing {tool.name}: {str(e)}", "details": str(e)}
    
    def _extract_tool_parameters(self, tool: BaseTool, message: str, state: ConstellationState) -> Dict[str, Any]:
        """Extract safe parameters for a tool from the message and state using security validation."""
        try:
            # Use the secure parameter validator
            return self.parameter_validator.extract_safe_parameters(tool.name, message, state)
        except ValidationError as e:
            logger.error(f"Parameter validation failed for {tool.name}: {e}")
            # Return safe fallback parameters
            return self._get_fallback_parameters(tool.name, state)
        except Exception as e:
            logger.error(f"Unexpected error in parameter extraction for {tool.name}: {e}")
            return self._get_fallback_parameters(tool.name, state)

    def _get_fallback_parameters(self, tool_name: str, state: ConstellationState) -> Dict[str, Any]:
        """Get safe fallback parameters when validation fails."""
        framework = getattr(state, 'framework', None)
        framework_name = framework.value if framework else 'langchain'

        fallback_params = {
            "create_code_file": {
                "content": f"# Safe example for {framework_name}\nprint('Hello from {framework_name}!')",
                "filename": f"safe_example_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py",
                "language": "python"
            },
            "auto_create_files_from_text": {
                "text": f"Generate safe {framework_name} example",
                "context": f"Framework: {framework_name}"
            },
            "start_practice_session": {
                "framework": framework_name,
                "user_id": "anonymous",
                "task_description": f"Safe practice session for {framework_name}"
            },
            "tavily_search": {
                "query": f"{framework_name} documentation",
                "max_results": 3
            },
            "search_framework_documentation": {
                "framework": framework_name,
                "topic": "getting started"
            }
        }

        return fallback_params.get(tool_name, {"input": "safe_fallback", "safe_mode": True})
            
        # Handle other common tool parameter extraction
        try:
            # Try to get required parameters from tool schema
            required_params = {}
            
            # Different tools might expose schema differently
            if hasattr(tool, "args_schema") and hasattr(tool.args_schema, "schema"):
                # For LangChain tools
                schema_dict = tool.args_schema.schema()
                required_params = schema_dict.get("properties", {})
            elif hasattr(tool, "schema"):
                # For other tools
                if callable(tool.schema):
                    schema_dict = tool.schema()
                else:
                    schema_dict = tool.schema
                required_params = schema_dict.get("properties", {})
            
            # Extract framework parameter if needed
            if "framework" in required_params:
                params["framework"] = state.framework.value if hasattr(state, 'framework') else "langchain"
                
            # Extract query parameter if needed
            if "query" in required_params:
                params["query"] = message
                
            # Extract user_id parameter if needed
            if "user_id" in required_params:
                params["user_id"] = state.user_id if hasattr(state, 'user_id') else "user_001"
                
            # Extract filename parameter if needed
            if "filename" in required_params:
                filename = self._extract_filename_from_message(message)
                if filename:
                    params["filename"] = filename
                    
            # Extract content parameter if needed
            if "content" in required_params:
                params["content"] = message
                
            # Extract language parameter if needed
            if "language" in required_params:
                # Default to python for code-related tools
                params["language"] = "python"
                
            # Extract base_dir parameter if needed
            if "base_dir" in required_params:
                params["base_dir"] = "generated_code"
                
            # Extract task_description parameter if needed
            if "task_description" in required_params:
                params["task_description"] = message
                
            # Extract urls parameter if needed
            if "urls" in required_params:
                urls = self._extract_urls_from_message(message)
                if urls:
                    params["urls"] = ",".join(urls)
                    
            # Extract base_url parameter if needed
            if "base_url" in required_params and "urls" in params:
                urls_list = params["urls"].split(",")
                if urls_list:
                    params["base_url"] = urls_list[0]
                    
            # Extract instructions parameter if needed
            if "instructions" in required_params:
                params["instructions"] = message
                
        except Exception as e:
            logger.warning(f"Error extracting parameters for {tool.name}: {e}")
            # Fallback to basic parameters
            if tool.name in ["tavily_search", "search_framework_documentation", "search_framework_syntax"]:
                params["query"] = message
                params["framework"] = state.framework.value if hasattr(state, 'framework') else "langchain"
            elif "create" in tool.name.lower() or "file" in tool.name.lower():
                params["content"] = message
                params["filename"] = self._extract_filename_from_message(message) or f"example_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            else:
                # Default fallback
                params["input"] = message
        
        return params
    
    def _extract_filename_from_message(self, message: str) -> Optional[str]:
        """Extract filename from message content."""
        # Look for patterns like "save as X.py" or "create file X.py"
        filename_patterns = [
            r"save\s+(?:as|to|in|into)?\s+[\"']?([a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+)[\"']?",
            r"create\s+(?:a\s+)?file\s+[\"']?([a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+)[\"']?",
            r"filename[:\s]+[\"']?([a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+)[\"']?",
            r"name\s+(?:the|this|it)\s+(?:file|code)\s+[\"']?([a-zA-Z0-9_\-\.]+\.[a-zA-Z0-9]+)[\"']?",
        ]
        
        for pattern in filename_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_topic_from_message(self, message: str) -> Optional[str]:
        """Extract topic from message for documentation search."""
        # Look for patterns like "about X" or "how to use X"
        topic_patterns = [
            r"about\s+([a-zA-Z0-9_\-]+)",
            r"how\s+to\s+use\s+([a-zA-Z0-9_\-]+)",
            r"learn\s+about\s+([a-zA-Z0-9_\-]+)",
            r"documentation\s+for\s+([a-zA-Z0-9_\-]+)",
        ]
        
        for pattern in topic_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _should_execute_tool(self, tool: BaseTool, message: str, state: ConstellationState) -> bool:
        """Determine if a tool should be executed based on the message and context.
        
        Args:
            tool: The tool to check
            message: User's message
            state: Current constellation state
            
        Returns:
            Boolean indicating whether the tool should be executed
        """
        message_lower = message.lower()
        
        # Prevent direct execution of file creation tools with user message as content
        if tool.name in ["create_code_file", "auto_create_files_from_text"]:
            # Don't directly execute these tools with user message
            # They should be called by other methods that properly generate code
            return False
            
        # Tool-specific execution criteria
        if tool.name in ["tavily_search", "search_framework_documentation"]:
            # Execute search tools when user asks questions or wants to learn about something
            search_indicators = [
                "how to", "what is", "explain", "tell me about", "search for",
                "find", "look up", "documentation for", "examples of", "learn about"
            ]
            return any(indicator in message_lower for indicator in search_indicators)
            
        elif tool.name == "start_practice_session":
            # Execute practice tools when user wants to practice or try something
            practice_indicators = [
                "practice", "try", "exercise", "hands-on", "interactive",
                "let me try", "i want to try", "let's practice", "i'm ready", 
                "code", "example", "implement", "write", "generate"
            ]
            return any(indicator in message_lower for indicator in practice_indicators)
            
        elif tool.name in ["tavily_extract", "extract_learning_content"]:
            # Execute extraction tools when message contains URLs
            return "http" in message_lower and ("://" in message_lower or "www." in message_lower)
            
        # Default behavior: don't execute the tool unless explicitly requested
        return False
    
    def _extract_urls_from_message(self, message: str) -> List[str]:
        """Extract URLs from a message.
        
        Args:
            message: The message to extract URLs from
            
        Returns:
            List of extracted URLs
        """
        url_pattern = r'https?://[^\s]+'
        return re.findall(url_pattern, message)
    
    async def _generate_response_with_tool_results(
        self, 
        system_prompt: str, 
        state: ConstellationState, 
        tool_results: List[Dict[str, Any]]
    ) -> str:
        """Generate response incorporating tool results."""
        
        # Build comprehensive context with tool results
        tools_context = ""
        if tool_results:
            tools_context = "\n\nTool Execution Results:\n"
            
            for result in tool_results:
                tool_name = result.get('tool', 'Unknown Tool')
                
                # Check if there was an error in tool execution
                if 'error' in result:
                    tools_context += f"\n## {tool_name} - Error\n"
                    tools_context += f"Error: {result['error']}\n"
                    continue
                    
                # Format the tool result based on tool type
                result_content = str(result.get('result', ''))[:2000]  # Limit length to avoid token overflow
                
                # Special handling for different tool types
                if 'search' in tool_name.lower():
                    tools_context += f"\n## {tool_name} - Search Results\n"
                    tools_context += f"{result_content}\n"
                    tools_context += "\nPlease analyze these search results and incorporate relevant information in your response.\n"
                
                elif 'extract' in tool_name.lower():
                    tools_context += f"\n## {tool_name} - Extracted Content\n"
                    tools_context += f"{result_content[:1000]}...\n"  # Limit length further for extracts
                    tools_context += "\nPlease analyze this extracted content and use it to answer the user's question accurately.\n"
                
                elif 'crawl' in tool_name.lower():
                    tools_context += f"\n## {tool_name} - Crawl Results\n"
                    tools_context += f"{result_content[:1000]}...\n"  # Limit length further for crawls
                    tools_context += "\nPlease use this crawled information to guide your response.\n"
                    
                elif 'practice' in tool_name.lower() or 'file' in tool_name.lower():
                    tools_context += f"\n## {tool_name} - Code Practice\n"
                    tools_context += f"{result_content}\n"
                    tools_context += "\nThe user now has a practice environment. Explain what they can do with it and guide them on next steps.\n"
                    
                elif 'execute' in tool_name.lower():
                    tools_context += f"\n## {tool_name} - Code Execution Results\n"
                    tools_context += f"```\n{result_content}\n```\n"
                    tools_context += "\nExplain these execution results to the user in a helpful way.\n"
                    
                else:
                    tools_context += f"\n## {tool_name} - Result\n"
                    tools_context += f"{result_content}\n"
        
            tools_context += "\n\nIMPORTANT: Focus on answering the user's question directly using the tool results above. Don't just summarize the results - provide meaningful insights, explanations, and guidance based on them."
        
        # Enhanced instructions for better response generation
        enhanced_prompt = system_prompt + "\n\nIMPORTANT INSTRUCTIONS FOR RESPONSE GENERATION:"
        enhanced_prompt += "\n1. Answer the user's question directly using the information from tool executions."
        enhanced_prompt += "\n2. Provide concrete examples, explanations and clear guidance."
        enhanced_prompt += "\n3. Focus on the specific framework they're asking about without unnecessary background."
        enhanced_prompt += "\n4. If tools were executed, refer to those results to provide accurate information."
        enhanced_prompt += "\n5. When code examples were created, explain how to use and modify them for learning."
        enhanced_prompt += tools_context
        
        # Generate conversation with tool context
        conversation = [
            SystemMessage(content=enhanced_prompt),
            *state.messages[-6:],  # Last 6 messages for context
        ]
        
        # Generate response
        try:
            response = await self.model.ainvoke(conversation)
            return response.content
        except Exception as e:
            logger.error(f"Error generating response with tool results: {e}")
            # Fallback to a simpler response
            return self._generate_fallback_response(tool_results)
            
    def _generate_fallback_response(self, tool_results: List[Dict[str, Any]]) -> str:
        """Generate a simple fallback response when the main response generation fails."""
        if not tool_results:
            return "I've processed your request, but I'm having trouble generating a detailed response. Could you rephrase your question or provide more information?"
            
        fallback = "Here are the results of the tools I ran for you:\n\n"
        
        for result in tool_results:
            tool_name = result.get('tool', 'Unknown Tool')
            
            if 'error' in result:
                fallback += f"- {tool_name}: There was an error - {result['error']}\n"
            else:
                result_content = str(result.get('result', ''))[:200]
                fallback += f"- {tool_name}: {result_content}...\n\n"
                
        fallback += "\nI hope this information is helpful. Feel free to ask if you need further details or explanation."
        return fallback
    
    def _analyze_for_handoff(self, response: str, state: ConstellationState) -> Optional[AgentRole]:
        """Analyze the response to suggest potential handoffs to other agents."""
        response_lower = response.lower()
        
        # Simple heuristics for handoff suggestions
        if any(word in response_lower for word in ["example", "code", "implementation", "show"]):
            return AgentRole.CODE_ASSISTANT
        elif any(word in response_lower for word in ["practice", "exercise", "try", "hands-on"]):
            return AgentRole.PRACTICE_FACILITATOR
        elif any(word in response_lower for word in ["documentation", "reference", "official", "docs"]):
            return AgentRole.DOCUMENTATION_EXPERT
        elif any(word in response_lower for word in ["error", "bug", "problem", "issue", "debug"]):
            return AgentRole.TROUBLESHOOTER
        elif any(word in response_lower for word in ["project", "build", "create", "application"]):
            return AgentRole.PROJECT_GUIDE
        elif any(word in response_lower for word in ["search", "find", "research", "latest"]):
            return AgentRole.RESEARCH_ASSISTANT
        
        return None
    
    def _calculate_confidence(self, response: str) -> float:
        """Calculate confidence score based on response content."""
        # Default confidence is 0.7
        confidence = 0.7
        
        # Adjust confidence based on response length
        if len(response) < 50:
            confidence -= 0.2
        elif len(response) > 1000:
            confidence += 0.1
            
        # Adjust confidence based on specific phrases
        uncertain_phrases = [
            "i'm not sure", "i don't know", "i'm uncertain", 
            "might be", "could be", "possibly", "i think", "i believe"
        ]
        
        confident_phrases = [
            "definitely", "certainly", "absolutely", "always", 
            "clearly", "precisely", "exactly", "specifically"
        ]
        
        # Reduce confidence for uncertain phrases
        for phrase in uncertain_phrases:
            if phrase in response.lower():
                confidence -= 0.1
                break
                
        # Increase confidence for confident phrases
        for phrase in confident_phrases:
            if phrase in response.lower():
                confidence += 0.1
                break
                
        # Ensure confidence is within bounds
        return max(0.1, min(1.0, confidence))
    
    def evaluate_confidence(self, state: ConstellationState) -> float:
        """
        Evaluate the agent's confidence in handling the current state.
        
        Args:
            state: Current constellation state
            
        Returns:
            float: Confidence score between 0 and 1
        """
        # Default confidence is 0.5
        confidence = 0.5
        
        # Get the latest user message
        user_messages = [msg for msg in state.messages if isinstance(msg, HumanMessage)]
        if not user_messages:
            return 0.3  # Low confidence if no user messages
            
        latest_message = user_messages[-1].content.lower()
        
        # Adjust confidence based on agent role and message content
        if self.role == AgentRole.CODE_ASSISTANT:
            if any(kw in latest_message for kw in ["code", "example", "implement", "write", "function", "class"]):
                confidence += 0.3
                
        elif self.role == AgentRole.DOCUMENTATION_EXPERT:
            if any(kw in latest_message for kw in ["document", "explain", "what is", "how does", "reference"]):
                confidence += 0.3
                
        elif self.role == AgentRole.INSTRUCTOR:
            if any(kw in latest_message for kw in ["learn", "teach", "explain", "concept", "understand"]):
                confidence += 0.3
                
        elif self.role == AgentRole.TROUBLESHOOTER:
            if any(kw in latest_message for kw in ["error", "bug", "fix", "issue", "problem", "doesn't work"]):
                confidence += 0.3
                
        elif self.role == AgentRole.PRACTICE_FACILITATOR:
            if any(kw in latest_message for kw in ["practice", "exercise", "try", "hands-on"]):
                confidence += 0.3
                
        elif self.role == AgentRole.RESEARCH_ASSISTANT:
            if any(kw in latest_message for kw in ["search", "find", "latest", "research", "current", "new"]):
                confidence += 0.3
                
        # Adjust confidence based on framework mentioned in message
        if state.framework.value.lower() in latest_message:
            confidence += 0.1
            
        # Ensure confidence is within bounds
        return max(0.1, min(1.0, confidence))
    
    def _generate_framework_code(self, framework: str, user_message: str) -> str:
        """Dynamically generate code examples based on framework and user request.
        
        Args:
            framework: The selected framework
            user_message: The user's request message
            
        Returns:
            Generated code as a string
        """
        # Extract code topic or features from user message
        topics = self._extract_topics_from_message(user_message)
        topic_str = ", ".join(topics) if topics else framework
        
        if framework.lower() == "langchain":
            if any(kw in user_message.lower() for kw in ["chat", "conversation", "llm", "gpt"]):
                code = """from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

# LangChain example for {0}
def create_langchain_chat():
    \"\"\"Create a simple LangChain chat example.\"\"\"
    
    # Initialize the LLM
    llm = ChatOpenAI(
        model="gpt-3.5-turbo",  # Replace with your preferred model
        temperature=0.7,
    )
    
    # Create a prompt template
    prompt = ChatPromptTemplate.from_template(
        "You are an assistant specialized in {topic}. "
        "Please answer the following question: {question}"
    )
    
    # Create a simple chain
    chain = prompt | llm
    
    return chain

if __name__ == "__main__":
    # Create the chain
    chain = create_langchain_chat()
    
    print("LangChain Chat Example")
    print("=" * 30)
    
    # Example usage (uncomment to run with your API key)
    # response = chain.invoke({
    #     "topic": "{0}",
    #     "question": "Explain the basic concepts in simple terms"
    # })
    # print(f"Response: {response.content}")
    
    print("Chain created successfully! Add your API key to run.")
"""
                return code.format(topic_str)
            else:
                code = """from langchain_core.prompts import PromptTemplate
from langchain_openai import OpenAI
from langchain_core.output_parsers import StrOutputParser

# LangChain example for {0}
def create_simple_chain():
    \"\"\"Create a simple LangChain chain example.\"\"\"
    
    # Create a prompt template
    prompt = PromptTemplate(
        input_variables=["topic"],
        template="Write a short explanation about {topic} in simple terms:"
    )
    
    # Create an LLM (replace with your API key)
    llm = OpenAI(temperature=0.7)
    
    # Create the chain
    output_parser = StrOutputParser()
    chain = prompt | llm | output_parser
    
    return chain

if __name__ == "__main__":
    # Create and use the chain
    my_chain = create_simple_chain()
    
    # Example usage
    print("LangChain Basic Example")
    print("=" * 30)
    
    # You can uncomment this if you have an API key:
    # result = my_chain.invoke({{"topic": "{0}"}})
    # print(f"Result: {{result}}")
    
    print("Chain created successfully! Add your API key to run.")
"""
                return code.format(topic_str)

        elif framework.lower() == "langgraph":
            code = """from typing import Dict, TypedDict, Annotated, Sequence
from langgraph.graph import StateGraph, END

# LangGraph example for {0}
class GraphState(TypedDict):
    \"\"\"State for the graph.\"\"\"
    message: str
    step: int
    result: str

# Define nodes
def start_node(state: GraphState) -> GraphState:
    \"\"\"Starting node for the graph.\"\"\"
    print("Starting the graph...")
    return {{"message": "Hello from start node!", "step": 1, "result": ""}}

def process_node(state: GraphState) -> GraphState:
    \"\"\"Process node that transforms the message.\"\"\"
    print(f"Processing step {{state['step']}}...")
    return {{
        "message": f"Processed: {{state['message']}}",
        "step": state["step"] + 1,
        "result": ""
    }}

def end_node(state: GraphState) -> GraphState:
    \"\"\"End node that finalizes the result.\"\"\"
    print(f"Ending with: {{state['message']}}")
    return {{
        **state,
        "result": f"Graph completed successfully! Final step: {{state['step']}}"
    }}

def create_basic_graph():
    \"\"\"Create a basic LangGraph example.\"\"\"
    # Create the graph
    graph = StateGraph(GraphState)
    
    # Add nodes
    graph.add_node("start", start_node)
    graph.add_node("process", process_node)
    graph.add_node("end", end_node)
    
    # Add edges
    graph.add_edge("start", "process")
    graph.add_edge("process", "end")
    graph.add_edge("end", END)
    
    # Set entry point
    graph.set_entry_point("start")
    
    # Compile the graph
    return graph.compile()

if __name__ == "__main__":
    # Create and run the graph
    print("LangGraph Basic Example for {0}")
    print("=" * 30)
    
    my_graph = create_basic_graph()
    result = my_graph.invoke({{}})
    print(f"Final result: {{result}}")
"""
            return code.format(topic_str)

        elif framework.lower() == "crewai":
            code = """from crewai import Agent, Task, Crew
from langchain_openai import ChatOpenAI

# CrewAI example for {0}

def create_crew_example():
    \"\"\"Create a simple CrewAI example.\"\"\"
    
    # Define your agents with roles and goals
    researcher = Agent(
        role='Research Analyst',
        goal='Discover the latest information about {0}',
        backstory='You are an expert research analyst with a keen eye for detail.',
        verbose=True,
        allow_delegation=False,
        # Uncomment to use with your API key:
        # llm=ChatOpenAI(model="gpt-4")
    )
    
    writer = Agent(
        role='Content Writer',
        goal='Create engaging content about {0}',
        backstory='You are a skilled content writer who can explain complex topics simply.',
        verbose=True,
        allow_delegation=False,
        # Uncomment to use with your API key:
        # llm=ChatOpenAI(model="gpt-4")
    )
    
    # Create tasks for your agents
    research_task = Task(
        description=f"Research the latest developments in {0} and create a summary of key points.",
        expected_output="A comprehensive research summary with key points about {0}.",
        agent=researcher
    )
    
    writing_task = Task(
        description=f"Using the research provided, create an engaging article about {0}.",
        expected_output="A well-written article explaining {0} in an engaging way.",
        agent=writer
    )
    
    # Create your crew with a sequential process
    crew = Crew(
        agents=[researcher, writer],
        tasks=[research_task, writing_task],
        verbose=True
    )
    
    return crew

if __name__ == "__main__":
    print("CrewAI Example for {0}")
    print("=" * 30)
    
    crew = create_crew_example()
    
    # Uncomment to run with your API key:
    # result = crew.kickoff()
    # print(f"Result: {{result}}")
    
    print("Crew created successfully! Add your API key to run.")
"""
            return code.format(topic_str)

        elif framework.lower() == "autogen":
            code = """import autogen

# AutoGen example for {0}

def create_autogen_example():
    \"\"\"Create a simple AutoGen example.\"\"\"
    
    # Configure the agents
    config_list = [
        {{
            "model": "gpt-3.5-turbo",
            # Uncomment and add your API key to run:
            # "api_key": "your-api-key-here"
        }}
    ]
    
    # Create an assistant agent
    assistant = autogen.AssistantAgent(
        name="Assistant",
        llm_config={{"config_list": config_list}}
    )
    
    # Create a user proxy agent
    user_proxy = autogen.UserProxyAgent(
        name="User",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=10,
        is_termination_msg=lambda x: "TERMINATE" in x.get("content", ""),
    )
    
    return assistant, user_proxy

if __name__ == "__main__":
    print("AutoGen Example for {0}")
    print("=" * 30)
    
    assistant, user_proxy = create_autogen_example()
    
    # Uncomment to run with your API key:
    # user_proxy.initiate_chat(
    #     assistant,
    #     message=f"Tell me about {0} and provide a simple code example. End with TERMINATE."
    # )
    
    print("AutoGen agents created successfully! Add your API key to run.")
"""
            return code.format(topic_str)
        else:
            # Generic example for any other framework
            code = """# Basic {0} example for {1}
print("Hello from {0}!")

# TODO: Add your {0} code here
def main():
    \"\"\"Main function for {0} practice.\"\"\"
    print("This is a basic {0} example focused on {1}")
    
    # Add your implementation here
    result = "Practice session started!"
    return result

if __name__ == "__main__":
    print("{0} Practice Session")
    print("=" * 30)
    
    result = main()
    print(f"Result: {{result}}")
    
    print("\\nGreat! You've started your {0} practice session.")
    print("Try modifying this code to experiment with {0} features!")
"""
            return code.format(framework, topic_str)

    def _extract_topics_from_message(self, message: str) -> list:
        """Extract potential code topics from a user message.
        
        Args:
            message: The user's message
            
        Returns:
            List of extracted topics
        """
        # Common code-related topics to look for
        topic_patterns = [
            r"(?:about|using|with|for|on)\s+(\w+(?:\s+\w+){0,3})",
            r"(\w+(?:\s+\w+){0,2})\s+(?:example|code|implementation)",
            r"how\s+to\s+(?:use|implement|create)\s+(\w+(?:\s+\w+){0,3})",
            r"(\w+(?:\s+\w+){0,2})\s+tutorial",
        ]
        
        topics = []
        for pattern in topic_patterns:
            matches = re.findall(pattern, message, re.IGNORECASE)
            topics.extend(matches)
        
        # Filter out common stop words and short words
        stop_words = {"the", "a", "an", "and", "or", "but", "if", "then", "else", "when", "so", "can", "you", "me", "i", "we"}
        filtered_topics = [
            topic for topic in topics 
            if len(topic) > 3 and topic.lower() not in stop_words
        ]
        
        return filtered_topics[:3] if filtered_topics else [] 