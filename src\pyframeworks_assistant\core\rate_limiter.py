"""
Rate limiting and API management system for external service calls.
Provides intelligent rate limiting, caching, and fallback mechanisms.
"""

import time
import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import hashlib
from pathlib import Path

logger = logging.getLogger(__name__)


class RateLimitStrategy(Enum):
    """Rate limiting strategies."""
    FIXED_WINDOW = "fixed_window"
    SLIDING_WINDOW = "sliding_window"
    TOKEN_BUCKET = "token_bucket"
    EXPONENTIAL_BACKOFF = "exponential_backoff"


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    max_requests: int = 10  # Maximum requests per window
    window_seconds: int = 60  # Time window in seconds
    strategy: RateLimitStrategy = RateLimitStrategy.SLIDING_WINDOW
    burst_limit: int = 5  # Maximum burst requests
    backoff_factor: float = 2.0  # Exponential backoff factor
    max_backoff: int = 300  # Maximum backoff time in seconds
    cache_ttl: int = 3600  # Cache TTL in seconds


@dataclass
class RequestRecord:
    """Record of API request."""
    timestamp: float
    success: bool
    response_time: float
    error: Optional[str] = None


class IntelligentCache:
    """Intelligent caching system for API responses."""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.max_memory_items = 1000
    
    def _get_cache_key(self, service: str, params: Dict[str, Any]) -> str:
        """Generate cache key from service and parameters."""
        # Create a stable hash from parameters
        param_str = json.dumps(params, sort_keys=True)
        param_hash = hashlib.md5(param_str.encode()).hexdigest()
        return f"{service}_{param_hash}"
    
    def _get_cache_file(self, cache_key: str) -> Path:
        """Get cache file path."""
        return self.cache_dir / f"{cache_key}.json"
    
    def get(self, service: str, params: Dict[str, Any], ttl: int = 3600) -> Optional[Any]:
        """Get cached response if available and not expired."""
        cache_key = self._get_cache_key(service, params)
        
        # Check memory cache first
        if cache_key in self.memory_cache:
            cached_data = self.memory_cache[cache_key]
            if time.time() - cached_data["timestamp"] < ttl:
                logger.debug(f"Cache hit (memory) for {service}")
                return cached_data["data"]
            else:
                # Expired, remove from memory cache
                del self.memory_cache[cache_key]
        
        # Check file cache
        cache_file = self._get_cache_file(cache_key)
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                
                if time.time() - cached_data["timestamp"] < ttl:
                    # Add to memory cache
                    self._add_to_memory_cache(cache_key, cached_data)
                    logger.debug(f"Cache hit (file) for {service}")
                    return cached_data["data"]
                else:
                    # Expired, remove file
                    cache_file.unlink()
            except Exception as e:
                logger.warning(f"Error reading cache file: {e}")
        
        return None
    
    def set(self, service: str, params: Dict[str, Any], data: Any):
        """Cache response data."""
        cache_key = self._get_cache_key(service, params)
        cached_data = {
            "timestamp": time.time(),
            "data": data
        }
        
        # Add to memory cache
        self._add_to_memory_cache(cache_key, cached_data)
        
        # Save to file cache
        try:
            cache_file = self._get_cache_file(cache_key)
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cached_data, f)
            logger.debug(f"Cached response for {service}")
        except Exception as e:
            logger.warning(f"Error writing cache file: {e}")
    
    def _add_to_memory_cache(self, cache_key: str, cached_data: Dict[str, Any]):
        """Add item to memory cache with size limit."""
        if len(self.memory_cache) >= self.max_memory_items:
            # Remove oldest item
            oldest_key = min(self.memory_cache.keys(), 
                           key=lambda k: self.memory_cache[k]["timestamp"])
            del self.memory_cache[oldest_key]
        
        self.memory_cache[cache_key] = cached_data
    
    def clear_expired(self, ttl: int = 3600):
        """Clear expired cache entries."""
        current_time = time.time()
        
        # Clear memory cache
        expired_keys = [
            key for key, data in self.memory_cache.items()
            if current_time - data["timestamp"] > ttl
        ]
        for key in expired_keys:
            del self.memory_cache[key]
        
        # Clear file cache
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                
                if current_time - cached_data["timestamp"] > ttl:
                    cache_file.unlink()
            except Exception as e:
                logger.warning(f"Error checking cache file {cache_file}: {e}")


class RateLimiter:
    """Advanced rate limiter with multiple strategies."""
    
    def __init__(self, service_name: str, config: RateLimitConfig):
        self.service_name = service_name
        self.config = config
        self.request_history: List[RequestRecord] = []
        self.last_request_time = 0.0
        self.current_backoff = 1.0
        self.consecutive_failures = 0
        self.cache = IntelligentCache()
    
    async def acquire(self, params: Dict[str, Any] = None) -> bool:
        """Acquire permission to make a request."""
        current_time = time.time()
        
        # Check cache first
        if params:
            cached_response = self.cache.get(self.service_name, params, self.config.cache_ttl)
            if cached_response is not None:
                return True  # Use cached response
        
        # Clean old request history
        self._clean_history(current_time)
        
        # Apply rate limiting strategy
        if self.config.strategy == RateLimitStrategy.SLIDING_WINDOW:
            return await self._sliding_window_check(current_time)
        elif self.config.strategy == RateLimitStrategy.TOKEN_BUCKET:
            return await self._token_bucket_check(current_time)
        elif self.config.strategy == RateLimitStrategy.EXPONENTIAL_BACKOFF:
            return await self._exponential_backoff_check(current_time)
        else:  # FIXED_WINDOW
            return await self._fixed_window_check(current_time)
    
    async def _sliding_window_check(self, current_time: float) -> bool:
        """Sliding window rate limiting."""
        window_start = current_time - self.config.window_seconds
        recent_requests = [r for r in self.request_history if r.timestamp > window_start]
        
        if len(recent_requests) >= self.config.max_requests:
            # Calculate wait time
            oldest_request = min(recent_requests, key=lambda r: r.timestamp)
            wait_time = self.config.window_seconds - (current_time - oldest_request.timestamp)
            
            if wait_time > 0:
                logger.info(f"Rate limit reached for {self.service_name}, waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
        
        return True
    
    async def _token_bucket_check(self, current_time: float) -> bool:
        """Token bucket rate limiting."""
        # Simple token bucket implementation
        time_passed = current_time - self.last_request_time
        tokens_to_add = time_passed * (self.config.max_requests / self.config.window_seconds)
        
        # For simplicity, we'll use request count as tokens
        available_tokens = min(self.config.burst_limit, 
                             self.config.burst_limit - len(self.request_history) + tokens_to_add)
        
        if available_tokens < 1:
            wait_time = 1.0 / (self.config.max_requests / self.config.window_seconds)
            logger.info(f"Token bucket empty for {self.service_name}, waiting {wait_time:.2f}s")
            await asyncio.sleep(wait_time)
        
        return True
    
    async def _exponential_backoff_check(self, current_time: float) -> bool:
        """Exponential backoff based on failures."""
        if self.consecutive_failures > 0:
            wait_time = min(self.current_backoff, self.config.max_backoff)
            logger.info(f"Exponential backoff for {self.service_name}, waiting {wait_time:.2f}s")
            await asyncio.sleep(wait_time)
        
        return True
    
    async def _fixed_window_check(self, current_time: float) -> bool:
        """Fixed window rate limiting."""
        window_start = (current_time // self.config.window_seconds) * self.config.window_seconds
        window_requests = [r for r in self.request_history if r.timestamp >= window_start]
        
        if len(window_requests) >= self.config.max_requests:
            wait_time = window_start + self.config.window_seconds - current_time
            if wait_time > 0:
                logger.info(f"Fixed window limit reached for {self.service_name}, waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
        
        return True
    
    def record_request(self, success: bool, response_time: float, error: str = None, 
                      params: Dict[str, Any] = None, response_data: Any = None):
        """Record the result of a request."""
        current_time = time.time()
        
        # Record request
        record = RequestRecord(
            timestamp=current_time,
            success=success,
            response_time=response_time,
            error=error
        )
        self.request_history.append(record)
        self.last_request_time = current_time
        
        # Update backoff based on success/failure
        if success:
            self.consecutive_failures = 0
            self.current_backoff = 1.0
            
            # Cache successful response
            if params and response_data:
                self.cache.set(self.service_name, params, response_data)
        else:
            self.consecutive_failures += 1
            self.current_backoff = min(
                self.current_backoff * self.config.backoff_factor,
                self.config.max_backoff
            )
    
    def _clean_history(self, current_time: float):
        """Clean old request history."""
        cutoff_time = current_time - (self.config.window_seconds * 2)  # Keep 2x window
        self.request_history = [r for r in self.request_history if r.timestamp > cutoff_time]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        current_time = time.time()
        recent_requests = [r for r in self.request_history 
                          if current_time - r.timestamp < self.config.window_seconds]
        
        successful_requests = [r for r in recent_requests if r.success]
        failed_requests = [r for r in recent_requests if not r.success]
        
        return {
            "service": self.service_name,
            "total_requests": len(recent_requests),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": len(successful_requests) / len(recent_requests) if recent_requests else 0,
            "average_response_time": sum(r.response_time for r in successful_requests) / len(successful_requests) if successful_requests else 0,
            "consecutive_failures": self.consecutive_failures,
            "current_backoff": self.current_backoff,
            "requests_in_window": len(recent_requests),
            "window_limit": self.config.max_requests
        }


class APIManager:
    """Centralized API management with rate limiting and caching."""
    
    def __init__(self):
        self.rate_limiters: Dict[str, RateLimiter] = {}
        self.default_configs = {
            "tavily_search": RateLimitConfig(
                max_requests=5,  # Reduced from default to be more conservative
                window_seconds=60,
                strategy=RateLimitStrategy.SLIDING_WINDOW,
                cache_ttl=1800,  # 30 minutes cache for search results
                backoff_factor=2.0,
                max_backoff=300
            ),
            "search_framework_documentation": RateLimitConfig(
                max_requests=10,
                window_seconds=60,
                strategy=RateLimitStrategy.SLIDING_WINDOW,
                cache_ttl=3600,  # 1 hour cache for documentation
                backoff_factor=1.5,
                max_backoff=120
            ),
            "default": RateLimitConfig(
                max_requests=20,
                window_seconds=60,
                strategy=RateLimitStrategy.SLIDING_WINDOW,
                cache_ttl=600,  # 10 minutes default cache
                backoff_factor=1.5,
                max_backoff=60
            )
        }
    
    def get_rate_limiter(self, service_name: str) -> RateLimiter:
        """Get or create rate limiter for a service."""
        if service_name not in self.rate_limiters:
            config = self.default_configs.get(service_name, self.default_configs["default"])
            self.rate_limiters[service_name] = RateLimiter(service_name, config)
        
        return self.rate_limiters[service_name]
    
    async def execute_with_rate_limit(self, service_name: str, func: Callable, 
                                    params: Dict[str, Any] = None, *args, **kwargs) -> Any:
        """Execute function with rate limiting and caching."""
        rate_limiter = self.get_rate_limiter(service_name)
        
        # Check cache first
        if params:
            cached_response = rate_limiter.cache.get(service_name, params, rate_limiter.config.cache_ttl)
            if cached_response is not None:
                logger.info(f"Using cached response for {service_name}")
                return cached_response
        
        # Acquire rate limit permission
        await rate_limiter.acquire(params)
        
        # Execute function
        start_time = time.time()
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            response_time = time.time() - start_time
            rate_limiter.record_request(True, response_time, params=params, response_data=result)
            
            return result
            
        except Exception as e:
            response_time = time.time() - start_time
            rate_limiter.record_request(False, response_time, str(e), params)
            raise
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all rate limiters."""
        return {
            name: limiter.get_stats() 
            for name, limiter in self.rate_limiters.items()
        }
    
    def clear_all_caches(self):
        """Clear all caches."""
        for limiter in self.rate_limiters.values():
            limiter.cache.clear_expired(0)  # Clear all


# Global API manager instance
api_manager = APIManager()
